// 胶囊翻译窗口逻辑
class CapsuleTranslate {
    constructor() {
        this.sourceText = null;
        this.resultText = null;
        this.sourceLangBtn = null;
        this.targetLangBtn = null;
        this.translateBtn = null;
        this.statusIndicator = null;
        this.pinBtn = null;
        this.closeBtn = null;
        
        this.currentSourceLang = 'auto';
        this.currentTargetLang = 'en';
        this.languages = [];
        this.config = null;
        this.isPinned = false;
        this.isTranslating = false;
        
        this.init();
    }

    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initElements());
        } else {
            this.initElements();
        }
    }

    initElements() {
        // 获取DOM元素
        this.sourceText = document.getElementById('source-text');
        this.resultText = document.getElementById('result-text');
        this.sourceLangBtn = document.getElementById('source-lang');
        this.targetLangBtn = document.getElementById('target-lang');
        this.translateBtn = document.getElementById('translate-btn');
        this.statusIndicator = document.getElementById('status');
        this.pinBtn = document.getElementById('pin-btn');
        this.closeBtn = document.getElementById('close-btn');

        if (!this.sourceText || !this.resultText || !this.translateBtn) {
            console.error('关键DOM元素未找到');
            return;
        }

        this.bindEvents();
        this.setupMessageHandlers();
        
        // 聚焦到输入框
        setTimeout(() => {
            this.sourceText.focus();
        }, 100);
    }

    bindEvents() {
        // 翻译按钮点击事件
        this.translateBtn.addEventListener('click', () => {
            this.performTranslate();
        });

        // 输入框回车事件
        this.sourceText.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                this.performTranslate();
            }
        });

        // 语言选择按钮事件
        this.sourceLangBtn.addEventListener('click', () => {
            this.showLanguageSelector('source');
        });

        this.targetLangBtn.addEventListener('click', () => {
            this.showLanguageSelector('target');
        });

        // 置顶按钮事件
        this.pinBtn.addEventListener('click', () => {
            this.togglePin();
        });

        // 关闭按钮事件
        this.closeBtn.addEventListener('click', () => {
            this.closeWindow();
        });

        // 结果文本双击复制
        this.resultText.addEventListener('dblclick', () => {
            if (this.resultText.value.trim()) {
                this.copyResult();
            }
        });

        // 输入文本变化事件
        this.sourceText.addEventListener('input', () => {
            this.updateTranslateButtonState();
        });
    }

    setupMessageHandlers() {
        // 设置消息处理函数
        window.handleTranslateResponse = (data) => {
            this.handleTranslateResponse(data);
        };

        window.handleConfigResponse = (config) => {
            this.handleConfigResponse(config);
        };

        window.handleLanguagesResponse = (languages) => {
            this.handleLanguagesResponse(languages);
        };

        window.handleSetText = (text) => {
            this.handleSetText(text);
        };

        window.handleThemeChange = (isDark) => {
            this.handleThemeChange(isDark);
        };
    }

    // 执行翻译
    performTranslate() {
        const text = this.sourceText.value.trim();
        if (!text) {
            this.updateStatus('请输入要翻译的文本');
            return;
        }

        if (this.isTranslating) {
            return;
        }

        this.setTranslating(true);
        this.updateStatus('正在翻译...');
        this.resultText.value = '';

        // 发送翻译请求到主窗口
        window.capsuleAPI.requestTranslate(text, this.currentSourceLang, this.currentTargetLang);
    }

    // 处理翻译响应
    handleTranslateResponse(data) {
        this.setTranslating(false);
        
        if (data.success) {
            this.resultText.value = data.text || '';
            this.updateStatus('翻译完成');
        } else {
            this.updateStatus('翻译失败: ' + (data.error || '未知错误'));
            window.capsuleAPI.showNotification('翻译失败: ' + (data.error || '未知错误'), 'error');
        }
    }

    // 处理配置响应
    handleConfigResponse(data) {
        this.config = data;
        console.log('收到配置:', data);
    }

    // 处理语言列表响应
    handleLanguagesResponse(languages) {
        this.languages = languages || [];
        console.log('收到语言列表:', this.languages);
    }

    // 处理设置文本
    handleSetText(text) {
        if (this.sourceText && text) {
            this.sourceText.value = text;
            this.updateTranslateButtonState();
            // 自动聚焦并选中文本
            this.sourceText.focus();
            this.sourceText.select();
        }
    }

    // 处理主题变化
    handleThemeChange(isDark) {
        document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
    }

    // 设置翻译状态
    setTranslating(isTranslating) {
        this.isTranslating = isTranslating;
        this.translateBtn.disabled = isTranslating;

        if (isTranslating) {
            this.translateBtn.textContent = '翻译中';
            this.translateBtn.classList.add('loading');
            document.body.classList.add('loading');
        } else {
            this.translateBtn.textContent = '翻译';
            this.translateBtn.classList.remove('loading');
            document.body.classList.remove('loading');
        }
    }

    // 更新翻译按钮状态
    updateTranslateButtonState() {
        const hasText = this.sourceText.value.trim().length > 0;
        this.translateBtn.disabled = !hasText || this.isTranslating;
    }

    // 更新状态指示器
    updateStatus(message) {
        if (this.statusIndicator) {
            this.statusIndicator.textContent = message;
        }
    }

    // 显示语言选择器（简化版）
    showLanguageSelector(type) {
        // 这里可以实现一个简单的语言选择逻辑
        // 暂时使用固定的几种常用语言
        const commonLanguages = [
            { code: 'auto', name: '自动检测' },
            { code: 'zh', name: '中文' },
            { code: 'en', name: '英语' },
            { code: 'ja', name: '日语' },
            { code: 'ko', name: '韩语' },
            { code: 'fr', name: '法语' },
            { code: 'de', name: '德语' },
            { code: 'es', name: '西班牙语' },
            { code: 'ru', name: '俄语' }
        ];

        // 简单的语言切换逻辑
        if (type === 'source') {
            const currentIndex = commonLanguages.findIndex(lang => lang.code === this.currentSourceLang);
            const nextIndex = (currentIndex + 1) % commonLanguages.length;
            this.currentSourceLang = commonLanguages[nextIndex].code;
            this.sourceLangBtn.textContent = commonLanguages[nextIndex].name;
        } else {
            const targetLanguages = commonLanguages.filter(lang => lang.code !== 'auto');
            const currentIndex = targetLanguages.findIndex(lang => lang.code === this.currentTargetLang);
            const nextIndex = (currentIndex + 1) % targetLanguages.length;
            this.currentTargetLang = targetLanguages[nextIndex].code;
            this.targetLangBtn.textContent = targetLanguages[nextIndex].name;
        }
    }

    // 切换置顶状态
    togglePin() {
        this.isPinned = !this.isPinned;
        window.capsuleAPI.togglePin(this.isPinned);

        // 更新按钮样式
        if (this.isPinned) {
            this.pinBtn.classList.add('pinned');
        } else {
            this.pinBtn.classList.remove('pinned');
        }

        this.updateStatus(this.isPinned ? '已置顶' : '取消置顶');

        // 显示临时通知
        this.showTemporaryNotification(this.isPinned ? '窗口已置顶' : '取消置顶');
    }

    // 关闭窗口
    closeWindow() {
        window.capsuleAPI.closeWindow();
    }

    // 复制翻译结果
    copyResult() {
        const text = this.resultText.value.trim();
        if (text) {
            window.capsuleAPI.copyText(text);
            this.showCopySuccess();
            window.capsuleAPI.showNotification('翻译结果已复制到剪贴板', 'success');
        }
    }

    // 显示临时通知
    showTemporaryNotification(message) {
        this.updateStatus(message);
        setTimeout(() => {
            this.updateStatus('就绪');
        }, 2000);
    }

    // 添加复制成功的视觉反馈
    showCopySuccess() {
        this.updateStatus('已复制到剪贴板');
        setTimeout(() => {
            this.updateStatus('就绪');
        }, 1500);
    }
}

// 初始化胶囊翻译应用
let capsuleApp;
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        capsuleApp = new CapsuleTranslate();
    });
} else {
    capsuleApp = new CapsuleTranslate();
}
