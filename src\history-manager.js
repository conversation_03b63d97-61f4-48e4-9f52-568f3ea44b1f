/**
 * 历史记录管理器
 * 负责管理OCR识别历史记录的存储、加载和显示
 * 优化版本：支持数据压缩、存储效率优化和向后兼容
 */
class HistoryManager {
    constructor() {
        this.histories = [];
        this.translateHistories = [];
        this.currentSelectedId = null;
        this.currentHistoryType = 'ocr'; // 'ocr' 或 'translate'
        this.updateMaxHistoryCount(); // 从配置中读取最大数量

        // 存储优化配置
        this.storageConfig = {
            // 历史记录存储键名
            ocrHistoryKey: 'ocr_histories',
            translateHistoryKey: 'translateHistory',
            // 数据压缩配置
            enableCompression: true,
            // 预览文本最大长度
            previewMaxLength: 50,
            // 结果文本压缩阈值（超过此长度的文本将被压缩存储）
            compressionThreshold: 1000,
            // 存储格式版本（用于数据迁移）
            storageVersion: '2.0'
        };

        // 定义模式图标SVG
        this.modeIcons = {
            ocr: `<svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 7V5a2 2 0 0 1 2-2h2"/>
                <path d="M17 3h2a2 2 0 0 1 2 2v2"/>
                <path d="M21 17v2a2 2 0 0 1-2 2h-2"/>
                <path d="M7 21H5a2 2 0 0 1-2-2v-2"/>
                <path d="M7 8h8"/>
                <path d="M7 12h10"/>
                <path d="M7 16h6"/>
            </svg>`,
            translate: `<svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m5 8 6 6"/>
                <path d="m4 14 6-6 2-3"/>
                <path d="M2 5h12"/>
                <path d="M7 2h1"/>
                <path d="m22 22-5-10-5 10"/>
                <path d="M14 18h6"/>
            </svg>`,
            select: `<svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 12l2 2 4-4"/>
                <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.39 0 4.56.94 6.16 2.46"/>
            </svg>`
        };

        this.init();
    }

    init() {
        this.loadHistories();
        this.loadTranslateHistories();
        this.bindEvents();
        // 初始化时更新记录数量显示
        this.updateHistoryCount();

        // 初始化后进行数据完整性检查和存储优化
        setTimeout(() => {
            this.validateDataIntegrity();

            // 检查是否需要存储优化
            const stats = this.getStorageStats();
            if (stats.limits.isNearLimit) {
                this.performStorageOptimization();
            }
        }, 1000);
    }

    // 绑定事件
    bindEvents() {
        // 历史记录页面的导航按钮
        document.getElementById('history-back-btn')?.addEventListener('click', () => {
            window.ocrPlugin.uiManager.showMainView();
        });

        document.getElementById('history-model-service-btn')?.addEventListener('click', () => {
            window.ocrPlugin.uiManager.showConfigView();
        });

        // 主题切换按钮事件已在ui.js中绑定，这里不需要重复绑定
        // document.getElementById('history-theme-toggle-btn')?.addEventListener('click', () => {
        //     window.ocrPlugin.uiManager.toggleTheme();
        // });

        document.getElementById('history-base-config-btn')?.addEventListener('click', () => {
            window.ocrPlugin.uiManager.showConfigView('base-config');
        });

        document.getElementById('history-history-btn')?.addEventListener('click', () => {
            window.ocrPlugin.uiManager.showHistoryView();
        });

        // 复制按钮
        document.getElementById('copy-btn-history')?.addEventListener('click', () => {
            this.copyHistoryResult();
        });

        // 翻译历史记录复制按钮
        document.getElementById('copy-btn-translate-source')?.addEventListener('click', () => {
            this.copyTranslateSourceResult();
        });

        document.getElementById('copy-btn-translate-target')?.addEventListener('click', () => {
            this.copyTranslateTargetResult();
        });

        // 历史记录类型切换按钮
        document.getElementById('history-ocr-btn')?.addEventListener('click', () => {
            this.switchHistoryType('ocr');
        });

        document.getElementById('history-translate-type-btn')?.addEventListener('click', () => {
            this.switchHistoryType('translate');
        });
    }

    // 从配置中更新最大历史记录数量
    updateMaxHistoryCount() {
        if (window.ocrPlugin && window.ocrPlugin.config) {
            const uiConfig = window.ocrPlugin.config.ui || {};
            this.maxHistoryCount = uiConfig.historyMaxCount || 100; // 默认为100
        } else {
            this.maxHistoryCount = 100; // 默认值
        }
    }

    // 添加新的历史记录
    addHistory(imageData, result, service, model, mode = '文字') {
        // 更新最大历史记录数量配置
        this.updateMaxHistoryCount();

        // 创建优化的历史记录对象
        const history = this.createOptimizedHistoryRecord({
            id: Date.now().toString(),
            timestamp: new Date(),
            service: service,
            model: model,
            mode: mode,
            result: result
        });

        this.histories.unshift(history); // 添加到开头

        // 限制历史记录数量（保持现有逻辑）
        if (this.histories.length > this.maxHistoryCount) {
            this.histories = this.histories.slice(0, this.maxHistoryCount);
        }

        this.saveHistories();

        // 如果当前在历史记录页面，刷新列表
        if (window.ocrPlugin.uiManager.currentView === 'history') {
            this.loadHistoryList();
        }
    }

    // 创建优化的历史记录对象
    createOptimizedHistoryRecord(data) {
        const { id, timestamp, service, model, mode, result } = data;

        // 基础记录结构
        const record = {
            id,
            timestamp,
            service,
            model,
            mode,
            // 存储格式版本标识
            _v: this.storageConfig.storageVersion
        };

        // 处理结果文本的存储
        if (result) {
            // 如果文本较长且启用压缩，进行压缩存储
            if (this.storageConfig.enableCompression &&
                result.length > this.storageConfig.compressionThreshold) {
                record.result = this.compressText(result);
                record._compressed = true;
            } else {
                record.result = result;
            }
        }

        // 生成预览文本
        record.preview = this.generatePreview(result, this.storageConfig.previewMaxLength);

        return record;
    }

    // 生成预览文本
    generatePreview(text, maxLength = 50) {
        if (!text) return '无内容';
        // 清理文本：移除多余的空白字符和换行符
        const cleanText = text.replace(/\s+/g, ' ').trim();
        return cleanText.length > maxLength ? cleanText.substring(0, maxLength) + '...' : cleanText;
    }

    // 文本压缩方法（简单的重复字符压缩）
    compressText(text) {
        if (!text || typeof text !== 'string') return text;

        try {
            // 简单的压缩策略：
            // 1. 压缩连续的空白字符
            // 2. 移除不必要的换行符
            // 3. 保留文本的基本结构
            let compressed = text
                .replace(/[ \t]+/g, ' ')  // 多个空格/制表符压缩为单个空格
                .replace(/\n\s*\n/g, '\n') // 多个连续换行压缩为单个换行
                .trim();

            // 如果压缩后仍然很长，进一步处理
            if (compressed.length > this.storageConfig.compressionThreshold * 2) {
                // 对于极长的文本，保留开头和结尾部分
                const keepLength = this.storageConfig.compressionThreshold;
                const halfKeep = Math.floor(keepLength / 2);
                compressed = compressed.substring(0, halfKeep) +
                           '\n...[内容已压缩]...\n' +
                           compressed.substring(compressed.length - halfKeep);
            }

            return compressed;
        } catch (error) {
            return text;
        }
    }

    // 文本解压缩方法
    decompressText(compressedText) {
        // 当前的压缩方法是可逆的，直接返回
        return compressedText;
    }

    // 统一的存储读取方法
    getStorageItem(key) {
        try {
            // 优先使用uTools的dbStorage，如果不可用则回退到localStorage
            if (typeof utools !== 'undefined' && utools.dbStorage && utools.dbStorage.getItem) {
                return utools.dbStorage.getItem(key);
            } else {
                return localStorage.getItem(key);
            }
        } catch (error) {
            console.warn(`读取存储项 ${key} 失败:`, error);
            return null;
        }
    }

    // 统一的存储写入方法
    setStorageItem(key, value) {
        try {
            // 优先使用uTools的dbStorage，如果不可用则回退到localStorage
            if (typeof utools !== 'undefined' && utools.dbStorage && utools.dbStorage.setItem) {
                utools.dbStorage.setItem(key, value);
                return true;
            } else {
                localStorage.setItem(key, value);
                return true;
            }
        } catch (error) {
            console.warn(`写入存储项 ${key} 失败:`, error);
            return false;
        }
    }

    // 从存储加载历史记录
    loadHistories() {
        try {
            const stored = this.getStorageItem(this.storageConfig.ocrHistoryKey);
            if (stored) {
                // uTools的dbStorage可能直接返回对象，localStorage返回字符串
                const data = typeof stored === 'string' ? JSON.parse(stored) : stored;
                let rawHistories = Array.isArray(data) ? data : [];

                // 数据迁移和兼容性处理
                const { migratedHistories, needsSave } = this.migrateHistoryData(rawHistories);
                this.histories = migratedHistories;

                // 如果数据被迁移，重新保存
                if (needsSave) {
                    this.saveHistories();
                }

                // 应用数量限制（确保不超过配置的最大数量）
                this.updateMaxHistoryCount();
                if (this.histories.length > this.maxHistoryCount) {
                    this.histories = this.histories.slice(0, this.maxHistoryCount);
                    this.saveHistories();
                }


            }
        } catch (error) {
            console.error('加载历史记录失败:', error);
            this.histories = [];
        }
    }

    // 历史记录数据迁移和兼容性处理
    migrateHistoryData(rawHistories) {
        let needsSave = false;
        const migratedHistories = rawHistories.map(history => {
            let migrated = { ...history };
            let recordChanged = false;

            // 转换时间戳为Date对象
            if (typeof migrated.timestamp === 'string') {
                migrated.timestamp = new Date(migrated.timestamp);
                recordChanged = true;
            }

            // 移除imageData字段（向后兼容）
            if (migrated.imageData) {
                delete migrated.imageData;
                recordChanged = true;
            }

            // 检查是否为旧格式记录（没有版本标识）
            if (!migrated._v) {
                // 为旧记录添加版本标识
                migrated._v = this.storageConfig.storageVersion;
                recordChanged = true;

                // 如果没有预览文本，生成预览
                if (!migrated.preview && migrated.result) {
                    migrated.preview = this.generatePreview(migrated.result, this.storageConfig.previewMaxLength);
                    recordChanged = true;
                }

                // 检查是否需要压缩长文本
                if (this.storageConfig.enableCompression &&
                    migrated.result &&
                    migrated.result.length > this.storageConfig.compressionThreshold &&
                    !migrated._compressed) {
                    migrated.result = this.compressText(migrated.result);
                    migrated._compressed = true;
                    recordChanged = true;
                }
            }

            if (recordChanged) {
                needsSave = true;
            }

            return migrated;
        });

        return { migratedHistories, needsSave };
    }

    // 保存历史记录到存储
    saveHistories() {
        try {
            // 准备存储数据
            const optimizedData = this.prepareDataForStorage(this.histories);

            // uTools的dbStorage可以直接存储数组，localStorage需要JSON序列化
            const isUToolsStorage = typeof utools !== 'undefined' && utools.dbStorage && utools.dbStorage.setItem;
            const dataToStore = isUToolsStorage ? optimizedData : JSON.stringify(optimizedData);

            // 计算数据大小用于监控
            const dataSize = JSON.stringify(optimizedData).length;

            // 检查是否接近uTools 1MB限制
            if (dataSize > 800 * 1024) { // 800KB警告阈值
                // 尝试进一步优化数据
                const furtherOptimized = this.emergencyOptimizeData(optimizedData);
                const finalDataToStore = isUToolsStorage ? furtherOptimized : JSON.stringify(furtherOptimized);

                this.setStorageItem(this.storageConfig.ocrHistoryKey, finalDataToStore);
                return;
            }

            // 尝试保存，如果失败则尝试紧急优化
            const success = this.setStorageItem(this.storageConfig.ocrHistoryKey, dataToStore);
            if (!success) {
                // 尝试紧急优化
                const emergencyData = this.emergencyOptimizeData(optimizedData);
                const emergencyDataToStore = isUToolsStorage ? emergencyData : JSON.stringify(emergencyData);
                this.setStorageItem(this.storageConfig.ocrHistoryKey, emergencyDataToStore);
            }
        } catch (error) {
            console.error('保存历史记录失败:', error);
        }
    }

    // 为存储准备优化的数据
    prepareDataForStorage(histories) {
        return histories.map(history => {
            // 创建存储副本，移除不必要的字段
            const storageRecord = {
                id: history.id,
                timestamp: history.timestamp,
                service: history.service,
                model: history.model,
                mode: history.mode,
                result: history.result,
                preview: history.preview,
                _v: history._v || this.storageConfig.storageVersion
            };

            // 如果记录被压缩，保留压缩标识
            if (history._compressed) {
                storageRecord._compressed = true;
            }

            return storageRecord;
        });
    }

    // 紧急数据优化（当接近存储限制时）
    emergencyOptimizeData(data) {
        return data.map(record => {
            const optimized = { ...record };

            // 进一步压缩长文本
            if (optimized.result && optimized.result.length > 500) {
                optimized.result = this.compressText(optimized.result);
                optimized._compressed = true;
            }

            // 缩短预览文本
            if (optimized.preview && optimized.preview.length > 30) {
                optimized.preview = optimized.preview.substring(0, 30) + '...';
            }

            return optimized;
        }).slice(0, Math.floor(this.maxHistoryCount * 0.8)); // 保留80%的记录
    }

    // 从存储加载翻译历史记录
    loadTranslateHistories() {
        try {
            const stored = this.getStorageItem(this.storageConfig.translateHistoryKey);
            if (stored) {
                // uTools的dbStorage可能直接返回对象，localStorage返回字符串
                const data = typeof stored === 'string' ? JSON.parse(stored) : stored;
                let rawHistories = Array.isArray(data) ? data : [];

                // 数据迁移和兼容性处理
                const { migratedHistories, needsSave } = this.migrateTranslateHistoryData(rawHistories);
                this.translateHistories = migratedHistories;

                // 如果数据被迁移，重新保存
                if (needsSave) {
                    this.saveTranslateHistories();
                }

                // 应用数量限制，确保不超过配置的最大数量
                this.updateMaxHistoryCount();
                if (this.translateHistories.length > this.maxHistoryCount) {
                    this.translateHistories = this.translateHistories.slice(0, this.maxHistoryCount);
                    this.saveTranslateHistories();
                }


            }
        } catch (error) {
            console.error('加载翻译历史记录失败:', error);
            this.translateHistories = [];
        }
    }

    // 翻译历史记录数据迁移和兼容性处理
    migrateTranslateHistoryData(rawHistories) {
        let needsSave = false;
        const migratedHistories = rawHistories.map(history => {
            let migrated = { ...history };
            let recordChanged = false;

            // 转换时间戳为Date对象
            if (typeof migrated.timestamp === 'string') {
                migrated.timestamp = new Date(migrated.timestamp);
                recordChanged = true;
            } else if (migrated.createdAt && typeof migrated.createdAt === 'number') {
                // 兼容旧格式
                migrated.timestamp = new Date(migrated.createdAt);
                delete migrated.createdAt;
                recordChanged = true;
            }

            // 检查是否为旧格式记录（没有版本标识）
            if (!migrated._v) {
                migrated._v = this.storageConfig.storageVersion;
                recordChanged = true;

                // 优化长文本存储
                if (this.storageConfig.enableCompression) {
                    if (migrated.sourceText && migrated.sourceText.length > this.storageConfig.compressionThreshold) {
                        migrated.sourceText = this.compressText(migrated.sourceText);
                        migrated._sourceCompressed = true;
                        recordChanged = true;
                    }
                    if (migrated.targetText && migrated.targetText.length > this.storageConfig.compressionThreshold) {
                        migrated.targetText = this.compressText(migrated.targetText);
                        migrated._targetCompressed = true;
                        recordChanged = true;
                    }
                }
            }

            if (recordChanged) {
                needsSave = true;
            }

            return migrated;
        });

        return { migratedHistories, needsSave };
    }

    // 保存翻译历史记录到存储
    saveTranslateHistories() {
        try {
            // 准备存储数据
            const optimizedData = this.prepareTranslateDataForStorage(this.translateHistories);

            // uTools的dbStorage可以直接存储数组，localStorage需要JSON序列化
            const isUToolsStorage = typeof utools !== 'undefined' && utools.dbStorage && utools.dbStorage.setItem;
            const dataToStore = isUToolsStorage ? optimizedData : JSON.stringify(optimizedData);

            // 计算数据大小用于监控
            const dataSize = JSON.stringify(optimizedData).length;

            // 检查是否接近存储限制
            if (dataSize > 800 * 1024) { // 800KB警告阈值
                // 尝试进一步优化数据
                const furtherOptimized = this.emergencyOptimizeTranslateData(optimizedData);
                const finalDataToStore = isUToolsStorage ? furtherOptimized : JSON.stringify(furtherOptimized);

                this.setStorageItem(this.storageConfig.translateHistoryKey, finalDataToStore);
                return;
            }

            this.setStorageItem(this.storageConfig.translateHistoryKey, dataToStore);
        } catch (error) {
            console.error('保存翻译历史记录失败:', error);
        }
    }

    // 为存储准备优化的翻译数据
    prepareTranslateDataForStorage(histories) {
        return histories.map(history => {
            const storageRecord = {
                id: history.id,
                timestamp: history.timestamp,
                sourceText: history.sourceText,
                targetText: history.targetText,
                sourceLanguage: history.sourceLanguage,
                targetLanguage: history.targetLanguage,
                service: history.service,
                model: history.model,
                _v: history._v || this.storageConfig.storageVersion
            };

            // 保留压缩标识
            if (history._sourceCompressed) storageRecord._sourceCompressed = true;
            if (history._targetCompressed) storageRecord._targetCompressed = true;

            return storageRecord;
        });
    }

    // 紧急翻译数据优化
    emergencyOptimizeTranslateData(data) {
        return data.map(record => {
            const optimized = { ...record };

            // 进一步压缩文本
            if (optimized.sourceText && optimized.sourceText.length > 300) {
                optimized.sourceText = this.compressText(optimized.sourceText);
                optimized._sourceCompressed = true;
            }
            if (optimized.targetText && optimized.targetText.length > 300) {
                optimized.targetText = this.compressText(optimized.targetText);
                optimized._targetCompressed = true;
            }

            return optimized;
        }).slice(0, Math.floor(this.maxHistoryCount * 0.8)); // 保留80%的记录
    }

    // 加载历史记录列表到界面
    loadHistoryList() {
        const historyList = document.getElementById('history-list');
        if (!historyList) return;

        // 如果是翻译记录，先同步最新数据
        if (this.currentHistoryType === 'translate') {
            this.syncTranslateHistories();
        }

        // 更新记录数量显示
        this.updateHistoryCount();

        const currentHistories = this.currentHistoryType === 'ocr' ? this.histories : this.translateHistories;

        if (currentHistories.length === 0) {
            const emptyText = this.currentHistoryType === 'ocr' ? '暂无识别记录' : '暂无翻译记录';
            const emptyHint = this.currentHistoryType === 'ocr' ? '完成OCR识别后记录将显示在这里' : '完成翻译后记录将显示在这里';
            const emptyIcon = this.modeIcons[this.currentHistoryType];

            historyList.innerHTML = `
                <div class="history-empty">
                    <div class="empty-icon">${emptyIcon}</div>
                    <div class="empty-text">${emptyText}</div>
                    <div class="empty-hint">${emptyHint}</div>
                </div>
            `;
            this.showEmptyDetail();
            return;
        }

        const historyItems = currentHistories.map(history => {
            const timeStr = this.formatTime(history.timestamp);
            let preview = '';

            if (this.currentHistoryType === 'ocr') {
                preview = history.preview;
            } else {
                // 翻译记录显示源文本预览
                preview = this.generatePreview(history.sourceText);
            }

            return `
                <div class="history-item" data-id="${history.id}" data-type="${this.currentHistoryType}">
                    <div class="history-item-content">
                        <div class="history-preview">${preview}</div>
                        <div class="history-meta-line">
                            <span class="history-time">${timeStr}</span>
                        </div>
                    </div>
                    <button class="history-delete-btn" data-id="${history.id}" title="删除记录">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6L6 18"/>
                            <path d="M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            `;
        }).join('');

        historyList.innerHTML = historyItems;

        // 绑定点击事件
        historyList.querySelectorAll('.history-item').forEach(item => {
            item.addEventListener('click', (e) => {
                // 如果点击的是删除按钮，不触发选择事件
                if (e.target.classList.contains('history-delete-btn')) {
                    return;
                }
                this.selectHistory(item.dataset.id, item.dataset.type);
            });
        });

        // 绑定删除按钮事件
        historyList.querySelectorAll('.history-delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡
                const historyId = btn.dataset.id;
                this.deleteHistory(historyId);
            });
        });

        // 默认选择第一个
        if (currentHistories.length > 0) {
            this.selectHistory(currentHistories[0].id, this.currentHistoryType);
        }
    }

    // 格式化时间显示
    formatTime(date) {
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${month}/${day} ${hours}:${minutes}`;
    }

    // 格式化模型显示（使用友好名称）
    formatModel(service, model) {
        const serviceNames = {
            'baidu': '百度智能云',
            'tencent': '腾讯云',
            'aliyun': '阿里云',
            'openai': 'OpenAI',
            'anthropic': 'Anthropic',
            'google': 'Gemini',
            'alibaba': '阿里云百炼',
            'bytedance': '火山引擎',
            'utools': 'uTools AI',
            'custom': '自定义平台'
        };

        // 对于AI模型，尝试获取友好的模型名称
        if (model && ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'utools'].includes(service)) {
            try {
                // 获取主插件实例
                const ocrPlugin = window.ocrPlugin;
                if (ocrPlugin && ocrPlugin.config && ocrPlugin.config[service]) {
                    const platformConfig = ocrPlugin.config[service];
                    const modelNameMap = platformConfig.modelNameMap || {};
                    const friendlyModelName = modelNameMap[model] || model;

                    // 返回友好的模型名称，不需要服务名称前缀（因为图标已经显示了服务商）
                    return friendlyModelName;
                }
            } catch (error) {
                // 如果获取友好名称失败，使用原始模型名称
                return model || (serviceNames[service] || service);
            }

            // 如果没有配置信息，返回原始模型名称
            return model;
        }

        // 对于传统OCR服务，返回服务名称
        return serviceNames[service] || service;
    }

    // 选择历史记录
    selectHistory(id, type = null) {
        // 更新选中状态
        document.querySelectorAll('.history-item').forEach(item => {
            item.classList.remove('active');
        });

        const selectedItem = document.querySelector(`[data-id="${id}"]`);
        if (selectedItem) {
            selectedItem.classList.add('active');
        }

        this.currentSelectedId = id;
        this.showHistoryDetail(id, type || this.currentHistoryType);
    }

    // 显示历史记录详情
    showHistoryDetail(id, type = 'ocr') {
        const histories = type === 'ocr' ? this.histories : this.translateHistories;
        const history = histories.find(h => h.id === id);
        if (!history) {
            this.showEmptyDetail();
            return;
        }

        const detailEmpty = document.getElementById('history-detail-empty');
        const resultText = document.getElementById('history-result-text');
        const copyBtn = document.getElementById('copy-btn-history');

        const ocrResultArea = document.getElementById('ocr-result-area');
        const translateResultArea = document.getElementById('translate-result-area');

        if (detailEmpty) detailEmpty.style.display = 'none';

        if (type === 'ocr') {
            // 显示OCR结果
            if (ocrResultArea) ocrResultArea.style.display = 'flex'; // 改为flex以正确布局
            if (translateResultArea) translateResultArea.style.display = 'none';
            if (resultText) {
                // 解压缩文本（如果需要）
                const displayText = this.getDisplayText(history.result, history._compressed);
                resultText.value = displayText || '';
            }
            if (copyBtn) {
                copyBtn.style.display = 'flex';
            }
        } else {
            // 显示翻译结果
            if (ocrResultArea) ocrResultArea.style.display = 'none';
            if (translateResultArea) translateResultArea.style.display = 'flex'; // 改为flex以正确布局

            const sourceText = document.getElementById('history-translate-source-text');
            const targetText = document.getElementById('history-translate-target-text');
            const translateSourceCopyBtn = document.getElementById('copy-btn-translate-source');
            const translateTargetCopyBtn = document.getElementById('copy-btn-translate-target');

            if (sourceText) {
                const displaySourceText = this.getDisplayText(history.sourceText, history._sourceCompressed);
                sourceText.value = displaySourceText || '';
            }
            if (targetText) {
                const displayTargetText = this.getDisplayText(history.targetText, history._targetCompressed);
                targetText.value = displayTargetText || '';
            }
            if (translateSourceCopyBtn) translateSourceCopyBtn.style.display = 'flex';
            if (translateTargetCopyBtn) translateTargetCopyBtn.style.display = 'flex';
            if (copyBtn) copyBtn.style.display = 'none';
        }
    }

    // 获取用于显示的文本（处理压缩文本的解压缩）
    getDisplayText(text, isCompressed = false) {
        if (!text) return '';

        // 如果文本被压缩，进行解压缩
        if (isCompressed) {
            return this.decompressText(text);
        }

        return text;
    }

    // 显示空详情
    showEmptyDetail() {
        const detailEmpty = document.getElementById('history-detail-empty');
        const ocrResultArea = document.getElementById('ocr-result-area');
        const translateResultArea = document.getElementById('translate-result-area');

        if (detailEmpty) {
            detailEmpty.style.display = 'flex';
            // 根据当前历史记录类型和记录数量更新空状态内容
            this.updateEmptyDetailContent();
        }
        if (ocrResultArea) ocrResultArea.style.display = 'none';
        if (translateResultArea) translateResultArea.style.display = 'none';

        // 隐藏所有复制按钮
        const copyBtn = document.getElementById('copy-btn-history');
        const translateSourceCopyBtn = document.getElementById('copy-btn-translate-source');
        const translateTargetCopyBtn = document.getElementById('copy-btn-translate-target');

        if (copyBtn) copyBtn.style.display = 'none';
        if (translateSourceCopyBtn) translateSourceCopyBtn.style.display = 'none';
        if (translateTargetCopyBtn) translateTargetCopyBtn.style.display = 'none';
    }

    // 更新空详情内容
    updateEmptyDetailContent() {
        const detailEmptyIcon = document.querySelector('.detail-empty-icon');
        const detailEmptyText = document.querySelector('.detail-empty-text');
        const detailEmptyHint = document.querySelector('.detail-empty-hint');

        if (!detailEmptyIcon || !detailEmptyText) return;

        const currentHistories = this.currentHistoryType === 'ocr' ? this.histories : this.translateHistories;

        if (currentHistories.length === 0) {
            // 没有记录时的空状态
            if (this.currentHistoryType === 'ocr') {
                detailEmptyIcon.innerHTML = this.modeIcons.ocr;
                detailEmptyText.textContent = '暂无识别记录';
                if (detailEmptyHint) detailEmptyHint.textContent = '完成OCR识别后记录将显示在这里';
            } else {
                detailEmptyIcon.innerHTML = this.modeIcons.translate;
                detailEmptyText.textContent = '暂无翻译记录';
                if (detailEmptyHint) detailEmptyHint.textContent = '完成翻译后记录将显示在这里';
            }
        } else {
            // 有记录但未选中时的空状态
            detailEmptyIcon.innerHTML = this.modeIcons.select;
            detailEmptyText.textContent = '选择左侧记录查看详情';
            if (detailEmptyHint) detailEmptyHint.textContent = '记录详情将在此处显示';
        }
    }

    // 复制历史记录结果
    copyHistoryResult() {
        const resultText = document.getElementById('history-result-text');
        if (resultText && resultText.value) {
            const handleCopySuccess = () => {
                window.ocrPlugin.showToast('复制成功', 'success');

                // 检查是否启用复制后自动关闭插件
                const config = window.ocrPlugin.config;
                if (config?.ui?.autoClose === true) {
                    // 延迟一点时间让用户看到复制成功的提示
                    setTimeout(() => {
                        window.ocrAPI?.hideMainWindow?.();
                    }, 500);
                }
            };

            navigator.clipboard.writeText(resultText.value).then(() => {
                handleCopySuccess();
            }).catch(() => {
                // 降级方案
                resultText.select();
                document.execCommand('copy');
                handleCopySuccess();
            });
        }
    }

    // 复制翻译历史记录原文
    copyTranslateSourceResult() {
        const sourceText = document.getElementById('history-translate-source-text');
        if (sourceText && sourceText.value) {
            const handleCopySuccess = () => {
                window.ocrPlugin.showToast('原文复制成功', 'success');

                // 检查是否启用复制后自动关闭插件
                const config = window.ocrPlugin.config;
                if (config?.ui?.autoClose === true) {
                    // 延迟一点时间让用户看到复制成功的提示
                    setTimeout(() => {
                        window.ocrAPI?.hideMainWindow?.();
                    }, 500);
                }
            };

            navigator.clipboard.writeText(sourceText.value).then(() => {
                handleCopySuccess();
            }).catch(() => {
                // 降级方案
                sourceText.select();
                document.execCommand('copy');
                handleCopySuccess();
            });
        }
    }

    // 复制翻译历史记录译文
    copyTranslateTargetResult() {
        const targetText = document.getElementById('history-translate-target-text');
        if (targetText && targetText.value) {
            const handleCopySuccess = () => {
                window.ocrPlugin.showToast('译文复制成功', 'success');

                // 检查是否启用复制后自动关闭插件
                const config = window.ocrPlugin.config;
                if (config?.ui?.autoClose === true) {
                    // 延迟一点时间让用户看到复制成功的提示
                    setTimeout(() => {
                        window.ocrAPI?.hideMainWindow?.();
                    }, 500);
                }
            };

            navigator.clipboard.writeText(targetText.value).then(() => {
                handleCopySuccess();
            }).catch(() => {
                // 降级方案
                targetText.select();
                document.execCommand('copy');
                handleCopySuccess();
            });
        }
    }

    // 切换历史记录类型
    switchHistoryType(type) {
        if (this.currentHistoryType === type) return;

        this.currentHistoryType = type;
        this.currentSelectedId = null;

        // 更新按钮状态
        document.querySelectorAll('.history-type-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-type="${type}"]`).classList.add('active');

        // 重新加载列表
        this.loadHistoryList();
    }

    // 删除指定历史记录（不需要确认）
    deleteHistory(historyId) {
        if (this.currentHistoryType === 'ocr') {
            this.histories = this.histories.filter(h => h.id !== historyId);
            this.saveHistories();
        } else {
            this.translateHistories = this.translateHistories.filter(h => h.id !== historyId);
            this.saveTranslateHistories();
        }

        // 如果删除的是当前选中的记录，清空选中状态
        if (this.currentSelectedId === historyId) {
            this.currentSelectedId = null;
            this.showEmptyDetail();
        }

        this.loadHistoryList();
    }

    // 删除当前历史记录
    deleteCurrentHistory() {
        if (!this.currentSelectedId) return;

        if (confirm('确定要删除这条历史记录吗？')) {
            this.deleteHistory(this.currentSelectedId);
        }
    }

    // 清空所有历史记录
    clearAllHistories() {
        const typeText = this.currentHistoryType === 'ocr' ? 'OCR记录' : '翻译记录';
        if (confirm(`确定要清空所有${typeText}吗？此操作不可恢复。`)) {
            if (this.currentHistoryType === 'ocr') {
                this.histories = [];
                this.saveHistories();
            } else {
                this.translateHistories = [];
                this.saveTranslateHistories();
            }
            this.currentSelectedId = null;
            this.loadHistoryList();
            this.showEmptyDetail();
        }
    }



    // 获取历史记录统计
    getStatistics() {
        const currentHistories = this.currentHistoryType === 'ocr' ? this.histories : this.translateHistories;

        if (this.currentHistoryType === 'ocr') {
            return {
                total: currentHistories.length,
                services: [...new Set(currentHistories.map(h => h.service))].length,
                lastWeek: currentHistories.filter(h =>
                    (new Date() - h.timestamp) < 7 * 24 * 60 * 60 * 1000
                ).length
            };
        } else {
            return {
                total: currentHistories.length,
                languages: [...new Set(currentHistories.map(h => h.targetLanguage))].length,
                lastWeek: currentHistories.filter(h =>
                    (new Date() - h.timestamp) < 7 * 24 * 60 * 60 * 1000
                ).length
            };
        }
    }

    // 更新历史记录数量显示
    updateHistoryCount() {
        const countText = document.getElementById('history-count-text');
        if (countText) {
            const currentHistories = this.currentHistoryType === 'ocr' ? this.histories : this.translateHistories;
            const count = currentHistories.length;
            const typeText = this.currentHistoryType === 'ocr' ? 'OCR记录' : '翻译记录';
            countText.textContent = `共 ${count} 条${typeText}`;
        }
    }

    // 同步翻译历史记录（从UI管理器获取最新数据）
    syncTranslateHistories() {
        if (window.ocrPlugin && window.ocrPlugin.uiManager) {
            const latestHistory = window.ocrPlugin.uiManager.getTranslateHistory();
            if (latestHistory && latestHistory.length > 0) {
                this.translateHistories = latestHistory.map(item => ({
                    ...item,
                    timestamp: item.timestamp ? new Date(item.timestamp) : new Date(item.createdAt || Date.now()),
                    _v: this.storageConfig.storageVersion // 确保新数据有版本标识
                }));
            }
        }
    }

    // 获取存储统计信息
    getStorageStats() {
        const ocrDataSize = JSON.stringify(this.histories).length;
        const translateDataSize = JSON.stringify(this.translateHistories).length;
        const totalSize = ocrDataSize + translateDataSize;

        return {
            ocr: {
                count: this.histories.length,
                sizeBytes: ocrDataSize,
                sizeKB: (ocrDataSize / 1024).toFixed(2),
                compressed: this.histories.filter(h => h._compressed).length
            },
            translate: {
                count: this.translateHistories.length,
                sizeBytes: translateDataSize,
                sizeKB: (translateDataSize / 1024).toFixed(2),
                compressed: this.translateHistories.filter(h => h._sourceCompressed || h._targetCompressed).length
            },
            total: {
                count: this.histories.length + this.translateHistories.length,
                sizeBytes: totalSize,
                sizeKB: (totalSize / 1024).toFixed(2),
                sizeMB: (totalSize / (1024 * 1024)).toFixed(3)
            },
            limits: {
                maxRecords: this.maxHistoryCount,
                warningThresholdKB: 800,
                maxThresholdKB: 1024,
                isNearLimit: totalSize > 800 * 1024
            }
        };
    }

    // 执行存储清理和优化
    performStorageOptimization() {
        let optimized = false;

        // 1. 清理超出数量限制的记录
        if (this.histories.length > this.maxHistoryCount) {
            this.histories = this.histories.slice(0, this.maxHistoryCount);
            optimized = true;
        }

        if (this.translateHistories.length > this.maxHistoryCount) {
            this.translateHistories = this.translateHistories.slice(0, this.maxHistoryCount);
            optimized = true;
        }

        // 2. 压缩长文本
        this.histories.forEach(history => {
            if (!history._compressed && history.result &&
                history.result.length > this.storageConfig.compressionThreshold) {
                history.result = this.compressText(history.result);
                history._compressed = true;
                optimized = true;
            }
        });

        this.translateHistories.forEach(history => {
            if (!history._sourceCompressed && history.sourceText &&
                history.sourceText.length > this.storageConfig.compressionThreshold) {
                history.sourceText = this.compressText(history.sourceText);
                history._sourceCompressed = true;
                optimized = true;
            }
            if (!history._targetCompressed && history.targetText &&
                history.targetText.length > this.storageConfig.compressionThreshold) {
                history.targetText = this.compressText(history.targetText);
                history._targetCompressed = true;
                optimized = true;
            }
        });

        // 3. 如果仍然接近限制，进行更激进的优化
        const afterStats = this.getStorageStats();
        if (afterStats.total.sizeBytes > 800 * 1024) {
            // 减少记录数量到80%
            const targetCount = Math.floor(this.maxHistoryCount * 0.8);
            if (this.histories.length > targetCount) {
                this.histories = this.histories.slice(0, targetCount);
                optimized = true;
            }
            if (this.translateHistories.length > targetCount) {
                this.translateHistories = this.translateHistories.slice(0, targetCount);
                optimized = true;
            }
        }

        if (optimized) {
            this.saveHistories();
            this.saveTranslateHistories();
        }

        return optimized;
    }

    // 格式化模型显示（使用友好名称）
    formatModel(service, model) {
        if (!service) return '未知';

        const serviceNames = {
            'baidu': '百度智能云',
            'tencent': '腾讯云',
            'aliyun': '阿里云',
            'openai': 'OpenAI',
            'anthropic': 'Anthropic',
            'google': 'Gemini',
            'alibaba': '阿里云百炼',
            'bytedance': '火山引擎',
            'utools': 'uTools AI',
            'custom': '自定义平台'
        };

        // 对于AI模型，尝试获取友好的模型名称
        if (model && ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'utools'].includes(service)) {
            try {
                // 获取主插件实例
                const ocrPlugin = window.ocrPlugin;
                if (ocrPlugin && ocrPlugin.config && ocrPlugin.config[service]) {
                    const platformConfig = ocrPlugin.config[service];
                    const modelNameMap = platformConfig.modelNameMap || {};
                    const friendlyModelName = modelNameMap[model] || model;

                    // 返回"服务商 - 友好模型名称"的格式
                    const serviceName = serviceNames[service] || service;
                    return `${serviceName} - ${friendlyModelName}`;
                }
            } catch (error) {
                // 如果获取友好名称失败，使用原始格式
                const serviceName = serviceNames[service] || service;
                return model ? `${serviceName} - ${model}` : serviceName;
            }

            // 如果没有配置信息，使用原始格式
            const serviceName = serviceNames[service] || service;
            return model ? `${serviceName} - ${model}` : serviceName;
        }

        // 对于传统OCR服务，只返回服务名称
        return serviceNames[service] || service;
    }

    // 获取存储统计信息
    getStorageStats() {
        const ocrDataSize = JSON.stringify(this.histories).length;
        const translateDataSize = JSON.stringify(this.translateHistories).length;
        const totalSize = ocrDataSize + translateDataSize;

        return {
            ocr: {
                count: this.histories.length,
                sizeBytes: ocrDataSize,
                sizeKB: (ocrDataSize / 1024).toFixed(2),
                compressed: this.histories.filter(h => h._compressed).length
            },
            translate: {
                count: this.translateHistories.length,
                sizeBytes: translateDataSize,
                sizeKB: (translateDataSize / 1024).toFixed(2),
                compressed: this.translateHistories.filter(h => h._sourceCompressed || h._targetCompressed).length
            },
            total: {
                count: this.histories.length + this.translateHistories.length,
                sizeBytes: totalSize,
                sizeKB: (totalSize / 1024).toFixed(2),
                sizeMB: (totalSize / (1024 * 1024)).toFixed(3)
            },
            limits: {
                maxRecords: this.maxHistoryCount,
                warningThresholdKB: 800,
                maxThresholdKB: 1024,
                isNearLimit: totalSize > 800 * 1024
            }
        };
    }

    // 执行存储清理和优化
    performStorageOptimization() {
        console.log('开始执行存储优化...');

        const beforeStats = this.getStorageStats();
        console.log('优化前统计:', beforeStats);

        let optimized = false;

        // 1. 清理超出数量限制的记录
        if (this.histories.length > this.maxHistoryCount) {
            this.histories = this.histories.slice(0, this.maxHistoryCount);
            optimized = true;
        }

        if (this.translateHistories.length > this.maxHistoryCount) {
            this.translateHistories = this.translateHistories.slice(0, this.maxHistoryCount);
            optimized = true;
        }

        // 2. 压缩长文本
        this.histories.forEach(history => {
            if (!history._compressed && history.result &&
                history.result.length > this.storageConfig.compressionThreshold) {
                history.result = this.compressText(history.result);
                history._compressed = true;
                optimized = true;
            }
        });

        this.translateHistories.forEach(history => {
            if (!history._sourceCompressed && history.sourceText &&
                history.sourceText.length > this.storageConfig.compressionThreshold) {
                history.sourceText = this.compressText(history.sourceText);
                history._sourceCompressed = true;
                optimized = true;
            }
            if (!history._targetCompressed && history.targetText &&
                history.targetText.length > this.storageConfig.compressionThreshold) {
                history.targetText = this.compressText(history.targetText);
                history._targetCompressed = true;
                optimized = true;
            }
        });

        // 3. 如果仍然接近限制，进行更激进的优化
        const afterStats = this.getStorageStats();
        if (afterStats.total.sizeBytes > 800 * 1024) {
            console.log('执行激进优化...');

            // 减少记录数量到80%
            const targetCount = Math.floor(this.maxHistoryCount * 0.8);
            if (this.histories.length > targetCount) {
                this.histories = this.histories.slice(0, targetCount);
                optimized = true;
            }
            if (this.translateHistories.length > targetCount) {
                this.translateHistories = this.translateHistories.slice(0, targetCount);
                optimized = true;
            }
        }

        if (optimized) {
            this.saveHistories();
            this.saveTranslateHistories();

            const finalStats = this.getStorageStats();
            console.log('优化后统计:', finalStats);
            console.log(`存储优化完成，节省了 ${((beforeStats.total.sizeBytes - finalStats.total.sizeBytes) / 1024).toFixed(2)} KB`);
        } else {
            console.log('无需优化，存储状态良好');
        }

        return optimized;
    }

    // 数据完整性检查和修复
    validateDataIntegrity() {
        let issues = [];
        let repaired = 0;

        // 检查和修复OCR历史记录
        this.histories = this.histories.filter((history, index) => {
            let hasIssue = false;

            if (!history.id) {
                issues.push(`OCR记录 ${index} 缺少ID`);
                hasIssue = true;
            }
            if (!history.timestamp || !(history.timestamp instanceof Date)) {
                if (history.timestamp) {
                    // 尝试修复时间戳
                    try {
                        history.timestamp = new Date(history.timestamp);
                        repaired++;
                    } catch (e) {
                        issues.push(`OCR记录 ${history.id || index} 时间戳格式错误且无法修复`);
                        hasIssue = true;
                    }
                } else {
                    issues.push(`OCR记录 ${history.id || index} 缺少时间戳`);
                    hasIssue = true;
                }
            }
            if (!history.result || history.result.trim() === '') {
                // 尝试从预览文本恢复结果
                if (history.preview && history.preview !== '无内容') {
                    history.result = history.preview.replace('...', '');
                    repaired++;
                } else {
                    issues.push(`OCR记录 ${history.id || index} 缺少结果文本且无法修复`);
                    hasIssue = true;
                }
            }

            // 过滤掉无法修复的记录
            return !hasIssue;
        });

        // 检查和修复翻译历史记录
        this.translateHistories = this.translateHistories.filter((history, index) => {
            let hasIssue = false;

            if (!history.id) {
                issues.push(`翻译记录 ${index} 缺少ID`);
                hasIssue = true;
            }
            if (!history.timestamp || !(history.timestamp instanceof Date)) {
                if (history.timestamp || history.createdAt) {
                    // 尝试修复时间戳
                    try {
                        history.timestamp = new Date(history.timestamp || history.createdAt);
                        if (history.createdAt) delete history.createdAt;
                        repaired++;
                    } catch (e) {
                        issues.push(`翻译记录 ${history.id || index} 时间戳格式错误且无法修复`);
                        hasIssue = true;
                    }
                } else {
                    issues.push(`翻译记录 ${history.id || index} 缺少时间戳`);
                    hasIssue = true;
                }
            }
            if (!history.sourceText && !history.targetText) {
                issues.push(`翻译记录 ${history.id || index} 缺少文本内容且无法修复`);
                hasIssue = true;
            }

            return !hasIssue;
        });

        // 如果有修复，保存数据
        if (repaired > 0) {
            this.saveHistories();
            this.saveTranslateHistories();
        }

        return issues;
    }
}

// 导出类
window.HistoryManager = HistoryManager;
