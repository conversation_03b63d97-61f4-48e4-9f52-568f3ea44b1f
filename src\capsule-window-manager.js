// 胶囊小窗管理器
class CapsuleWindowManager {
    constructor() {
        this.capsuleWindow = null;
        this.isWindowPinned = false;
        this.windowConfig = {
            width: 400,
            height: 500,
            minWidth: 300,
            minHeight: 400,
            maxWidth: 600,
            maxHeight: 800,
            resizable: true,
            frame: false,
            transparent: true,
            alwaysOnTop: false,
            skipTaskbar: true,
            webPreferences: {
                preload: 'capsule-preload.js',
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false
            }
        };
    }

    // 创建胶囊小窗
    createCapsuleWindow(initialText = '') {
        if (this.capsuleWindow) {
            // 如果窗口已存在，显示并聚焦
            this.showCapsuleWindow();
            if (initialText) {
                this.setCapsuleText(initialText);
            }
            return this.capsuleWindow;
        }

        try {
            // 使用 uTools API 创建窗口
            this.capsuleWindow = utools.createBrowserWindow(
                'capsule-translate.html',
                this.windowConfig,
                () => {
                    console.log('胶囊小窗创建成功');
                    this.setupCapsuleWindowEvents();
                    this.showCapsuleWindow();
                    
                    // 如果有初始文本，设置到输入框
                    if (initialText) {
                        setTimeout(() => {
                            this.setCapsuleText(initialText);
                        }, 500);
                    }
                }
            );

            // 设置窗口位置（右上角）
            this.positionCapsuleWindow();

            return this.capsuleWindow;
        } catch (error) {
            console.error('创建胶囊小窗失败:', error);
            return null;
        }
    }

    // 设置窗口位置
    positionCapsuleWindow() {
        if (!this.capsuleWindow) return;

        try {
            // 尝试获取屏幕尺寸
            let screenWidth = 1920;
            let screenHeight = 1080;

            if (typeof screen !== 'undefined' && screen.width && screen.height) {
                screenWidth = screen.width;
                screenHeight = screen.height;
            } else if (typeof window !== 'undefined' && window.screen) {
                screenWidth = window.screen.width;
                screenHeight = window.screen.height;
            }

            // 计算窗口位置（右上角，留出边距）
            const x = screenWidth - this.windowConfig.width - 20;
            const y = 20;

            this.capsuleWindow.setPosition(x, y);
        } catch (error) {
            console.error('设置窗口位置失败:', error);
            // 备用方案：使用默认位置
            try {
                this.capsuleWindow.center();
            } catch (centerError) {
                console.error('居中窗口失败:', centerError);
            }
        }
    }

    // 设置胶囊窗口事件监听
    setupCapsuleWindowEvents() {
        if (!this.capsuleWindow) return;

        // 监听窗口关闭事件
        this.capsuleWindow.on('closed', () => {
            console.log('胶囊小窗已关闭');
            this.capsuleWindow = null;
            this.isWindowPinned = false;
        });

        // 使用uTools的消息监听机制
        // 注意：在uTools环境中，我们需要通过不同的方式处理消息
        if (typeof window !== 'undefined') {
            // 设置全局消息处理器
            window.handleCapsuleMessage = (channel, data) => {
                switch (channel) {
                    case 'capsule-close':
                        this.closeCapsuleWindow();
                        break;
                    case 'capsule-toggle-pin':
                        this.togglePin(data);
                        break;
                    case 'capsule-translate-request':
                        this.handleTranslateRequest(data);
                        break;
                    case 'capsule-get-config':
                        this.sendConfigToCapsule();
                        break;
                    case 'capsule-get-languages':
                        this.sendLanguagesToCapsule();
                        break;
                    case 'capsule-copy-text':
                        this.copyTextToClipboard(data);
                        break;
                    case 'capsule-show-notification':
                        this.showNotification(data.message, data.type);
                        break;
                }
            };
        }
    }

    // 显示胶囊小窗
    showCapsuleWindow() {
        if (this.capsuleWindow) {
            this.capsuleWindow.show();
            this.capsuleWindow.focus();
        }
    }

    // 隐藏胶囊小窗
    hideCapsuleWindow() {
        if (this.capsuleWindow) {
            this.capsuleWindow.hide();
        }
    }

    // 关闭胶囊小窗
    closeCapsuleWindow() {
        if (this.capsuleWindow) {
            this.capsuleWindow.close();
            this.capsuleWindow = null;
            this.isWindowPinned = false;
        }
    }

    // 切换置顶状态
    togglePin(isPinned) {
        if (this.capsuleWindow) {
            this.isWindowPinned = isPinned;
            this.capsuleWindow.setAlwaysOnTop(isPinned);
            console.log(`胶囊窗口置顶状态: ${isPinned}`);
        }
    }

    // 设置胶囊窗口文本
    setCapsuleText(text) {
        if (this.capsuleWindow && text) {
            this.capsuleWindow.webContents.send('capsule-set-text', text);
        }
    }

    // 处理翻译请求
    async handleTranslateRequest(data) {
        try {
            const { text, sourceLang, targetLang } = data;
            
            // 获取主程序的翻译服务
            const ocrPlugin = window.ocrPlugin;
            if (!ocrPlugin || !ocrPlugin.ocrServices) {
                throw new Error('翻译服务不可用');
            }

            // 获取当前翻译模型配置
            const currentModel = ocrPlugin.configManager.getCurrentTranslateModel();
            if (!currentModel) {
                throw new Error('请先配置翻译模型');
            }

            // 获取配置
            const config = ocrPlugin.configManager.getConfig();
            
            // 执行翻译
            let result;
            if (currentModel.type === 'traditional') {
                // 传统翻译API
                const translateConfig = ocrPlugin.configManager.getTraditionalTranslateConfig(currentModel.service);
                result = await ocrPlugin.ocrServices.performTraditionalTranslation(
                    text,
                    sourceLang || 'auto',
                    targetLang || 'en',
                    currentModel.service,
                    translateConfig
                );
            } else {
                // AI翻译
                result = await ocrPlugin.ocrServices.performTranslation(
                    text,
                    currentModel.service,
                    currentModel.model,
                    config,
                    null, // 不使用流式输出
                    { langCode: targetLang || 'en', value: targetLang || 'English' }
                );
            }

            // 发送翻译结果到胶囊窗口
            if (this.capsuleWindow) {
                this.capsuleWindow.webContents.send('capsule-translate-response', {
                    success: result.success,
                    text: result.text || result.result,
                    error: result.error
                });
            }

        } catch (error) {
            console.error('翻译请求处理失败:', error);
            if (this.capsuleWindow) {
                this.capsuleWindow.webContents.send('capsule-translate-response', {
                    success: false,
                    error: error.message
                });
            }
        }
    }

    // 发送配置到胶囊窗口
    sendConfigToCapsule() {
        if (!this.capsuleWindow) return;

        try {
            const ocrPlugin = window.ocrPlugin;
            if (ocrPlugin && ocrPlugin.configManager) {
                const config = ocrPlugin.configManager.getConfig();
                const currentModel = ocrPlugin.configManager.getCurrentTranslateModel();
                
                this.capsuleWindow.webContents.send('capsule-config-response', {
                    config,
                    currentModel
                });
            }
        } catch (error) {
            console.error('发送配置失败:', error);
        }
    }

    // 发送语言列表到胶囊窗口
    sendLanguagesToCapsule() {
        if (!this.capsuleWindow) return;

        try {
            // 获取语言配置
            const languages = window.ocrPlugin?.languageConfig?.getLanguages() || [];
            this.capsuleWindow.webContents.send('capsule-languages-response', languages);
        } catch (error) {
            console.error('发送语言列表失败:', error);
        }
    }

    // 复制文本到剪贴板
    copyTextToClipboard(text) {
        try {
            if (window.ocrAPI && window.ocrAPI.copyText) {
                window.ocrAPI.copyText(text);
            } else if (typeof utools !== 'undefined' && utools.copyText) {
                utools.copyText(text);
            }
        } catch (error) {
            console.error('复制文本失败:', error);
        }
    }

    // 显示通知
    showNotification(message, type = 'info') {
        try {
            if (window.ocrAPI && window.ocrAPI.showNotification) {
                window.ocrAPI.showNotification(message, type);
            } else if (typeof utools !== 'undefined' && utools.showNotification) {
                utools.showNotification(message);
            }
        } catch (error) {
            console.error('显示通知失败:', error);
        }
    }

    // 检查胶囊窗口是否存在
    isCapsuleWindowOpen() {
        return this.capsuleWindow !== null;
    }

    // 获取胶囊窗口实例
    getCapsuleWindow() {
        return this.capsuleWindow;
    }
}

// 导出胶囊窗口管理器
window.CapsuleWindowManager = CapsuleWindowManager;
