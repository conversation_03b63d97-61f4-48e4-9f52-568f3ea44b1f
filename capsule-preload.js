// 胶囊小窗预加载脚本
let ipc<PERSON>enderer;
try {
    ipcRenderer = require('electron').ipc<PERSON>enderer;
} catch (e) {
    console.log('Electron ipcRenderer 不可用，使用备用通信方式');
}

// 胶囊小窗API
window.capsuleAPI = {
    // 发送消息到父窗口
    sendToParent: (channel, ...args) => {
        try {
            if (typeof utools !== 'undefined' && utools.sendToParent) {
                utools.sendToParent(channel, ...args);
            } else if (window.parent && window.parent !== window) {
                // 备用方案：使用postMessage
                window.parent.postMessage({ channel, args }, '*');
            } else {
                console.warn('无法发送消息到父窗口');
            }
        } catch (error) {
            console.error('发送消息失败:', error);
        }
    },

    // 监听来自父窗口的消息
    onMessage: (channel, callback) => {
        try {
            if (ipcRenderer) {
                ipcRenderer.on(channel, callback);
            } else {
                // 备用方案：使用window消息监听
                window.addEventListener('message', (event) => {
                    if (event.data && event.data.channel === channel) {
                        callback(event, event.data.data);
                    }
                });
            }
        } catch (error) {
            console.error('设置消息监听失败:', error);
        }
    },

    // 移除消息监听器
    removeListener: (channel, callback) => {
        try {
            if (ipcRenderer) {
                ipcRenderer.removeListener(channel, callback);
            }
        } catch (error) {
            console.error('移除消息监听失败:', error);
        }
    },

    // 关闭胶囊窗口
    closeWindow: () => {
        window.capsuleAPI.sendToParent('capsule-close');
    },

    // 置顶/取消置顶窗口
    togglePin: (isPinned) => {
        window.capsuleAPI.sendToParent('capsule-toggle-pin', isPinned);
    },

    // 请求翻译
    requestTranslate: (text, sourceLang, targetLang) => {
        window.capsuleAPI.sendToParent('capsule-translate-request', {
            text,
            sourceLang,
            targetLang
        });
    },

    // 获取翻译配置
    getTranslateConfig: () => {
        window.capsuleAPI.sendToParent('capsule-get-config');
    },

    // 获取语言列表
    getLanguageList: () => {
        window.capsuleAPI.sendToParent('capsule-get-languages');
    },

    // 复制文本到剪贴板
    copyText: (text) => {
        window.capsuleAPI.sendToParent('capsule-copy-text', text);
    },

    // 显示通知
    showNotification: (message, type = 'info') => {
        window.capsuleAPI.sendToParent('capsule-show-notification', { message, type });
    },

    // 检查是否为深色主题
    isDarkTheme: () => {
        if (typeof utools !== 'undefined' && utools.isDarkColors) {
            return utools.isDarkColors();
        }
        return window.matchMedia('(prefers-color-scheme: dark)').matches;
    },

    // 监听主题变化
    onThemeChange: (callback) => {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', callback);
            return () => mediaQuery.removeEventListener('change', callback);
        }
        return () => {};
    }
};

// 监听父窗口消息
window.capsuleAPI.onMessage('capsule-translate-response', (_, data) => {
    // 翻译结果回调
    if (window.handleTranslateResponse) {
        window.handleTranslateResponse(data);
    }
});

window.capsuleAPI.onMessage('capsule-config-response', (_, config) => {
    // 配置信息回调
    if (window.handleConfigResponse) {
        window.handleConfigResponse(config);
    }
});

window.capsuleAPI.onMessage('capsule-languages-response', (_, languages) => {
    // 语言列表回调
    if (window.handleLanguagesResponse) {
        window.handleLanguagesResponse(languages);
    }
});

window.capsuleAPI.onMessage('capsule-set-text', (_, text) => {
    // 设置文本内容
    if (window.handleSetText) {
        window.handleSetText(text);
    }
});

window.capsuleAPI.onMessage('capsule-theme-changed', (_, isDark) => {
    // 主题变化
    if (window.handleThemeChange) {
        window.handleThemeChange(isDark);
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 请求初始配置
    window.capsuleAPI.getTranslateConfig();
    window.capsuleAPI.getLanguageList();
    
    // 设置初始主题
    const isDark = window.capsuleAPI.isDarkTheme();
    if (window.handleThemeChange) {
        window.handleThemeChange(isDark);
    }
    
    // 监听主题变化
    window.capsuleAPI.onThemeChange((e) => {
        if (window.handleThemeChange) {
            window.handleThemeChange(e.matches);
        }
    });
});

// 错误处理
window.addEventListener('error', (event) => {
    console.error('胶囊窗口错误:', event.error);
    window.capsuleAPI.showNotification('发生错误: ' + event.error.message, 'error');
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
    window.capsuleAPI.showNotification('操作失败: ' + event.reason, 'error');
});

console.log('胶囊小窗预加载脚本已加载');
