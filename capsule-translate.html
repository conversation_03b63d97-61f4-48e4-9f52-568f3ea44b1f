<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译胶囊</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        /* 胶囊小窗专用样式 */
        body {
            margin: 0;
            padding: 8px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-color, #ffffff);
            color: var(--text-color, #333333);
            overflow: hidden;
        }

        .capsule-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 16px);
            border-radius: 12px;
            background: var(--bg-color, #ffffff);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color, #e0e0e0);
        }

        .capsule-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: var(--header-bg, #f8f9fa);
            border-radius: 12px 12px 0 0;
            border-bottom: 1px solid var(--border-color, #e0e0e0);
            min-height: 32px;
        }

        .capsule-title {
            font-size: 12px;
            font-weight: 500;
            color: var(--text-secondary, #666);
            margin: 0;
        }

        .capsule-controls {
            display: flex;
            gap: 4px;
        }

        .capsule-btn {
            width: 20px;
            height: 20px;
            border: none;
            border-radius: 4px;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }

        .capsule-btn:hover {
            background: var(--hover-bg, #e9ecef);
        }

        .capsule-btn svg {
            width: 12px;
            height: 12px;
            stroke: var(--text-secondary, #666);
        }

        .capsule-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 12px;
            gap: 8px;
            overflow: hidden;
        }

        .translate-input-area {
            flex: 1;
            min-height: 80px;
        }

        .translate-result-area {
            flex: 1;
            min-height: 80px;
        }

        .capsule-textarea {
            width: 100%;
            height: 100%;
            border: 1px solid var(--border-color, #e0e0e0);
            border-radius: 8px;
            padding: 8px;
            font-size: 13px;
            line-height: 1.4;
            resize: none;
            outline: none;
            background: var(--input-bg, #ffffff);
            color: var(--text-color, #333);
            transition: border-color 0.2s;
        }

        .capsule-textarea:focus {
            border-color: var(--primary-color, #007bff);
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
        }

        .capsule-textarea::placeholder {
            color: var(--text-placeholder, #999);
        }

        .translate-controls {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 4px 0;
            gap: 8px;
        }

        .language-selector {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .language-btn {
            padding: 4px 8px;
            border: 1px solid var(--border-color, #e0e0e0);
            border-radius: 6px;
            background: var(--input-bg, #ffffff);
            color: var(--text-color, #333);
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .language-btn:hover {
            background: var(--hover-bg, #f8f9fa);
            border-color: var(--primary-color, #007bff);
        }

        .translate-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            background: var(--primary-color, #007bff);
            color: white;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .translate-btn:hover {
            background: var(--primary-hover, #0056b3);
        }

        .translate-btn:disabled {
            background: var(--disabled-bg, #ccc);
            cursor: not-allowed;
        }

        .status-indicator {
            font-size: 11px;
            color: var(--text-secondary, #666);
            padding: 2px 6px;
            border-radius: 4px;
            background: var(--status-bg, #f8f9fa);
        }

        /* 深色主题 */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #1e1e1e;
                --text-color: #ffffff;
                --text-secondary: #b3b3b3;
                --text-placeholder: #666;
                --border-color: #404040;
                --header-bg: #2d2d2d;
                --input-bg: #2d2d2d;
                --hover-bg: #404040;
                --primary-color: #0d6efd;
                --primary-hover: #0b5ed7;
                --disabled-bg: #555;
                --status-bg: #2d2d2d;
            }
        }

        /* 加载状态 */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading .translate-btn {
            background: var(--disabled-bg, #ccc);
        }

        /* 动画效果 */
        .capsule-container {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 翻译按钮加载动画 */
        .translate-btn.loading::after {
            content: '';
            width: 12px;
            height: 12px;
            margin-left: 8px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 结果文本区域的复制提示 */
        .translate-result-area {
            position: relative;
        }

        .copy-hint {
            position: absolute;
            top: 8px;
            right: 8px;
            background: var(--primary-color, #007bff);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            opacity: 0;
            transition: opacity 0.2s;
            pointer-events: none;
        }

        .translate-result-area:hover .copy-hint {
            opacity: 0.8;
        }

        /* 快捷键提示 */
        .shortcut-hint {
            font-size: 10px;
            color: var(--text-secondary, #999);
            text-align: center;
            margin-top: 4px;
        }

        /* 置顶状态样式 */
        .capsule-btn.pinned {
            background: var(--primary-color, #007bff) !important;
            color: white !important;
        }

        /* 响应式设计 */
        @media (max-height: 400px) {
            .capsule-content {
                padding: 8px;
                gap: 6px;
            }

            .translate-input-area,
            .translate-result-area {
                min-height: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="capsule-container">
        <!-- 标题栏 -->
        <div class="capsule-header">
            <h1 class="capsule-title">翻译胶囊</h1>
            <div class="capsule-controls">
                <button class="capsule-btn" id="pin-btn" title="置顶">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 2l3.09 6.26L22 9l-5 4.74L18.18 22 12 18.77 5.82 22 7 13.74 2 9l6.91-.74L12 2z"/>
                    </svg>
                </button>
                <button class="capsule-btn" id="close-btn" title="关闭">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="capsule-content">
            <!-- 输入区域 -->
            <div class="translate-input-area">
                <textarea 
                    id="source-text" 
                    class="capsule-textarea" 
                    placeholder="输入要翻译的文本..."
                    spellcheck="false"
                ></textarea>
            </div>

            <!-- 控制栏 -->
            <div class="translate-controls">
                <div class="language-selector">
                    <button class="language-btn" id="source-lang">自动检测</button>
                    <span>→</span>
                    <button class="language-btn" id="target-lang">英语</button>
                </div>
                <button class="translate-btn" id="translate-btn">翻译</button>
            </div>

            <!-- 结果区域 -->
            <div class="translate-result-area">
                <textarea
                    id="result-text"
                    class="capsule-textarea"
                    placeholder="翻译结果将显示在这里..."
                    readonly
                ></textarea>
                <div class="copy-hint">双击复制</div>
            </div>

            <!-- 状态栏 -->
            <div class="status-indicator" id="status">就绪</div>

            <!-- 快捷键提示 -->
            <div class="shortcut-hint">Ctrl+Enter 翻译 | 双击结果复制</div>
        </div>
    </div>

    <script src="src/capsule-translate.js"></script>
</body>
</html>
