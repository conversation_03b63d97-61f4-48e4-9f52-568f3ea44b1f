<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR文字识别</title>
    <link rel="stylesheet" href="assets/style.css">
    <!-- KaTeX CSS - 本地资源 -->
    <link rel="stylesheet" href="assets/katex/katex.min.css">
    <!-- KaTeX JavaScript - 本地资源 -->
    <script defer src="assets/katex/katex.min.js"></script>
    <!-- html2canvas for image export - 本地资源 -->
    <script defer src="assets/html2canvas/html2canvas.min.js"></script>
</head>
<body>
    <div id="app">
        <!-- 主界面 -->
        <div id="main-view" class="view">
            <div class="main-container">
                <!-- 内容区域 -->
                <div class="content-container">
                    <!-- 左侧操作面板 -->
                    <div class="left-panel">
                        <!-- 图片预览区域 -->
                        <div class="image-preview-area">
                            <div id="image-preview" class="image-preview" style="display: none;">
                                <img id="preview-img" class="preview-img" alt="预览图片">
                            </div>
                            <div id="preview-placeholder" class="preview-placeholder">
                                <div class="placeholder-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                        <circle cx="9" cy="9" r="2"/>
                                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                                    </svg>
                                </div>
                                <div class="placeholder-text">图片预览区域</div>
                                <div class="placeholder-hint">支持滚轮缩放、拖拽移动、双击重置</div>
                            </div>
                        </div>

                    <!-- 底部控制区域 -->
                    <div class="bottom-controls">
                        <div class="action-buttons">
                            <button id="screenshot-btn" class="action-btn">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor" stroke="none">
                                    <!-- 左上角L形标记 -->
                                    <rect x="2" y="2" width="2" height="8" rx="1"/>
                                    <rect x="2" y="2" width="8" height="2" rx="1"/>
                                    <!-- 右上角L形标记 -->
                                    <rect x="20" y="2" width="2" height="8" rx="1"/>
                                    <rect x="14" y="2" width="8" height="2" rx="1"/>
                                    <!-- 左下角L形标记 -->
                                    <rect x="2" y="14" width="2" height="8" rx="1"/>
                                    <rect x="2" y="20" width="8" height="2" rx="1"/>
                                    <!-- 右下角L形标记 -->
                                    <rect x="20" y="14" width="2" height="8" rx="1"/>
                                    <rect x="14" y="20" width="8" height="2" rx="1"/>
                                </svg>
                                <span class="btn-text">截图</span>
                            </button>
                            <button id="upload-btn" class="action-btn">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/>
                                    <path d="M14 2v4a2 2 0 0 0 2 2h4"/>
                                </svg>
                                <span class="btn-text">选择</span>
                            </button>
                            <input type="file" id="file-input" accept="image/*" style="display: none;">
                        </div>

                        <div class="status-info">
                            <div class="status-item">
                                <span class="status-label">模型:</span>
                                <div class="service-switch-container">
                                    <button id="current-service" class="status-value service-switch-btn" title="点击切换服务">
                                        <span class="service-name">去配置模型</span>
                                        <span class="switch-icon">▼</span>
                                    </button>
                                    <div id="service-switch-menu" class="service-switch-menu">
                                        <!-- 动态生成的服务列表 -->
                                    </div>
                                </div>
                            </div>
                            <div class="status-item">
                                <span class="status-label">状态:</span>
                                <span id="recognition-status" class="status-value ready">就绪</span>
                            </div>
                        </div>
                    </div>

                    <div id="loading" class="loading-panel" style="display: none;">
                        <div class="loading-content">
                            <div class="spinner"></div>
                            <div class="loading-text">
                                <div class="loading-title">正在识别中...</div>
                                <div class="loading-desc">请稍候，正在处理您的图片</div>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- 右侧结果面板 -->
                    <div class="right-panel">
                        <div class="result-header" style="display: none;">
                        </div>

                    <div class="result-content">
                        <!-- 单栏模式（文字识别） -->
                        <div id="single-result-container" class="single-result-container">
                            <textarea id="result-text" class="result-text" placeholder="识别结果将显示在这里，您可以直接编辑..."></textarea>
                        </div>

                        <!-- 双栏模式（公式、表格、Markdown） -->
                        <div id="dual-result-container" class="dual-result-container" style="display: none;">
                            <!-- 上栏：渲染结果 -->
                            <div class="rendered-result-section">
                                <div id="rendered-result" class="rendered-result-content"></div>
                            </div>

                            <!-- 中间：操作按钮区域 -->
                            <div id="dual-action-buttons" class="dual-action-buttons" style="display: none;">
                                <!-- 按钮将通过JavaScript动态添加 -->
                            </div>

                            <!-- 下栏：原始结果 -->
                            <div class="raw-result-section">
                                <textarea id="raw-result-text" class="raw-result-text" placeholder="原始识别结果将显示在这里，您可以直接编辑..."></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="result-controls">
                        <div class="left-controls">
                            <button id="config-btn" class="control-btn">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                            <button id="history-btn" class="control-btn" title="查看历史记录">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect width="8" height="4" x="8" y="2" rx="1" ry="1"/>
                                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                                    <path d="M12 11h4"/>
                                    <path d="M12 16h4"/>
                                    <path d="M8 11h.01"/>
                                    <path d="M8 16h.01"/>
                                </svg>
                            </button>
                            <div class="recognition-mode-container">
                                <button id="recognition-mode-btn" class="control-btn" title="选择识别模式">
                                    <svg id="mode-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="btn-icon">
                                        <path d="M15 12h6"/>
                                        <path d="M15 6h6"/>
                                        <path d="m3 13 3.553-7.724a.5.5 0 0 1 .894 0L11 13"/>
                                        <path d="M3 18h18"/>
                                        <path d="M3.92 11h6.16"/>
                                    </svg>
                                    <span class="mode-text">文字识别</span>
                                    <span class="mode-arrow">▼</span>
                                </button>
                                <div id="recognition-mode-menu" class="recognition-mode-menu">
                                    <div class="mode-option" data-mode="text">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mode-icon">
                                            <path d="M15 12h6"/>
                                            <path d="M15 6h6"/>
                                            <path d="m3 13 3.553-7.724a.5.5 0 0 1 .894 0L11 13"/>
                                            <path d="M3 18h18"/>
                                            <path d="M3.92 11h6.16"/>
                                        </svg>
                                        <span>文字识别</span>
                                    </div>
                                    <div class="mode-option" data-mode="table">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mode-icon">
                                            <path d="M15 3v18"/>
                                            <rect width="18" height="18" x="3" y="3" rx="2"/>
                                            <path d="M21 9H3"/>
                                            <path d="M21 15H3"/>
                                        </svg>
                                        <span>表格识别</span>
                                    </div>
                                    <div class="mode-option" data-mode="formula">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mode-icon">
                                            <path d="M3 20h4.5a.5.5 0 0 0 .5-.5v-.282a.52.52 0 0 0-.247-.437 8 8 0 1 1 8.494-.001.52.52 0 0 0-.247.438v.282a.5.5 0 0 0 .5.5H21"/>
                                        </svg>
                                        <span>公式识别</span>
                                    </div>
                                    <div class="mode-option" data-mode="markdown">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mode-icon">
                                            <rect width="18" height="18" x="3" y="3" rx="2"/>
                                            <g transform="rotate(90 12 12)">
                                                <path d="M16 8.9V7H8l4 5-4 5h8v-1.9"/>
                                            </g>
                                        </svg>
                                        <span>MD识别</span>
                                    </div>
                                </div>
                            </div>
                            <button id="linebreak-toggle-btn" class="control-btn" title="保留换行符" data-enabled="true">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <!-- 保留换行符图标 -->
                                    <g class="linebreak-icon-enabled">
                                        <path d="m16 16-2 2 2 2"/>
                                        <path d="M3 12h15a3 3 0 1 1 0 6h-4"/>
                                        <path d="M3 18h7"/>
                                        <path d="M3 6h18"/>
                                    </g>
                                    <!-- 去除换行符图标（带斜线） -->
                                    <g class="linebreak-icon-disabled">
                                        <path d="m16 16-2 2 2 2"/>
                                        <path d="M3 12h15a3 3 0 1 1 0 6h-4"/>
                                        <path d="M3 18h7"/>
                                        <path d="M3 6h18"/>
                                        <line x1="2" x2="22" y1="2" y2="22"/>
                                    </g>
                                </svg>
                            </button>

                        </div>
                        <div class="center-controls">
                        </div>
                        <div class="right-controls">
                            <button id="re-recognize-btn" class="control-btn" title="重新识别">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m17 2 4 4-4 4"/>
                                    <path d="M3 11v-1a4 4 0 0 1 4-4h14"/>
                                    <path d="m7 22-4-4 4-4"/>
                                    <path d="M21 13v1a4 4 0 0 1-4 4H3"/>
                                </svg>
                            </button>
                            <button id="clear-btn" class="control-btn" title="清空结果">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m14.622 17.897-10.68-2.913"/>
                                    <path d="M18.376 2.622a1 1 0 1 1 3.002 3.002L17.36 9.643a.5.5 0 0 0 0 .707l.944.944a2.41 2.41 0 0 1 0 3.408l-.944.944a.5.5 0 0 1-.707 0L8.354 7.348a.5.5 0 0 1 0-.707l.944-.944a2.41 2.41 0 0 1 3.408 0l.944.944a.5.5 0 0 0 .707 0z"/>
                                    <path d="M9 8c-1.804 2.71-3.97 3.46-6.583 3.948a .507.507 0 0 0-.302.819l7.32 8.883a1 1 0 0 0 1.185.204C12.735 20.405 16 16.792 16 15"/>
                                </svg>
                            </button>
                            <button id="translate-btn" class="control-btn" title="翻译">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m5 8 6 6"/>
                                    <path d="m4 14 6-6 2-3"/>
                                    <path d="M2 5h12"/>
                                    <path d="M7 2h1"/>
                                    <path d="m22 22-5-10-5 10"/>
                                    <path d="M14 18h6"/>
                                </svg>
                                <span class="btn-text">翻译</span>
                            </button>
                            <button id="copy-btn" class="control-btn" title="复制结果">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                                    <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                                </svg>
                                <span class="btn-text">复制</span>
                            </button>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 配置界面 -->
        <div id="config-view" class="view" style="display: none;">
            <div class="main-container">
                <!-- 内容区域 -->
                <div class="content-container">

                    <!-- 基础配置页面 -->
                    <div id="base-config-page" class="config-page" style="display: none;">
                        <!-- 左侧配置选择面板 -->
                        <div class="left-panel">
                            <div class="config-list-area">
                                <div class="config-list">
                                    <div class="config-item active" data-config="personal-center">
                                        <div class="config-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                                                <circle cx="12" cy="8" r="5"/>
                                                <path d="M20 21a8 8 0 0 0-16 0"/>
                                            </svg>
                                        </div>
                                        <span class="config-name">个人中心</span>
                                    </div>
                                    <div class="config-item" data-config="default-model">
                                        <div class="config-icon">
                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                                                <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"/>
                                                <path d="m3.3 7 8.7 5 8.7-5"/>
                                                <path d="M12 22V12"/>
                                            </svg>
                                        </div>
                                        <span class="config-name">OCR默认模型</span>
                                    </div>
                                    <div class="config-item" data-config="translate-model">
                                        <div class="config-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                                                <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20"/>
                                                <path d="m8 13 4-7 4 7"/>
                                                <path d="M9.1 11h5.7"/>
                                            </svg>
                                        </div>
                                        <span class="config-name">翻译默认模型</span>
                                    </div>
                                    <div class="config-item" data-config="optional-settings">
                                        <div class="config-icon">
                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                                                <path d="M13 7 8.7 2.7a2.41 2.41 0 0 0-3.4 0L2.7 5.3a2.41 2.41 0 0 0 0 3.4L7 13"/>
                                                <path d="m8 6 2-2"/>
                                                <path d="m18 16 2-2"/>
                                                <path d="m17 11 4.3 4.3c.94.94.94 2.46 0 3.4l-2.6 2.6c-.94.94-2.46.94-3.4 0L11 17"/>
                                                <path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"/>
                                                <path d="m15 5 4 4"/>
                                            </svg>
                                        </div>
                                        <span class="config-name">可选配置</span>
                                    </div>
                                    <div class="config-item" data-config="shortcuts">
                                        <div class="config-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                                                <path d="M10 8h.01"/>
                                                <path d="M12 12h.01"/>
                                                <path d="M14 8h.01"/>
                                                <path d="M16 12h.01"/>
                                                <path d="M18 8h.01"/>
                                                <path d="M6 8h.01"/>
                                                <path d="M7 16h10"/>
                                                <path d="M8 12h.01"/>
                                                <rect width="20" height="16" x="2" y="4" rx="2"/>
                                            </svg>
                                        </div>
                                        <span class="config-name">快捷键配置</span>
                                    </div>
                                    <div class="config-item" data-config="backup-restore">
                                        <div class="config-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database-backup-icon lucide-database-backup">
                                                <ellipse cx="12" cy="5" rx="9" ry="3"/>
                                                <path d="M3 12a9 3 0 0 0 5 2.69"/>
                                                <path d="M21 9.3V5"/>
                                                <path d="M3 5v14a9 3 0 0 0 6.47 2.88"/>
                                                <path d="M12 12v4h4"/>
                                                <path d="M13 20a5 5 0 0 0 9-3 4.5 4.5 0 0 0-4.5-4.5c-1.33 0-2.54.54-3.41 1.41L12 16"/>
                                            </svg>
                                        </div>
                                        <span class="config-name">数据备份与恢复</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧配置内容面板 -->
                        <div class="right-panel">
                            <!-- 配置内容区域 -->
                            <div class="config-content-area">
                                <div class="config-content">
                                    <!-- 个人中心配置 -->
                                    <div id="personal-center-config" class="base-config-section" style="display: block;">
                                        <!-- 用户信息区域 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <circle cx="12" cy="8" r="5"/>
                                                        <path d="M20 21a8 8 0 0 0-16 0"/>
                                                    </svg>
                                                </div>
                                                <h4 id="user-info-title">用户信息</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div class="user-profile-section">
                                                    <!-- 左侧：头像和用户名 -->
                                                    <div class="user-avatar-container">
                                                        <div class="user-avatar clickable" id="user-avatar" title="点击更换头像">
                                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                <circle cx="12" cy="8" r="5"/>
                                                                <path d="M20 21a8 8 0 0 0-16 0"/>
                                                            </svg>
                                                        </div>
                                                        <!-- 用户名移动到头像下方 -->
                                                        <div class="user-name-container">
                                                            <input type="text" id="user-name" class="user-name-input" placeholder="请输入用户名" value="OCR用户" title="双击编辑用户名" readonly>
                                                        </div>
                                                    </div>

                                                    <!-- 右侧：功能区域 -->
                                                    <div class="user-functions-area">
                                                        <!-- OCR免费额度进度条 -->
                                                        <div class="usage-progress-item">
                                                            <div class="progress-header">
                                                                <span class="progress-label" id="ocr-quota-title">OCR免费额度</span>
                                                                <span class="progress-text" id="ocr-usage-text">30/30</span>
                                                            </div>
                                                            <div class="progress-bar">
                                                                <div class="progress-fill" id="ocr-progress-fill" style="width: 100%"></div>
                                                            </div>
                                                        </div>

                                                        <!-- 翻译免费额度进度条 -->
                                                        <div class="usage-progress-item">
                                                            <div class="progress-header">
                                                                <span class="progress-label" id="translate-quota-title">翻译免费额度</span>
                                                                <span class="progress-text" id="translate-usage-text">30/30</span>
                                                            </div>
                                                            <div class="progress-bar">
                                                                <div class="progress-fill" id="translate-progress-fill" style="width: 100%"></div>
                                                            </div>
                                                        </div>


                                                    </div>
                                                </div>
                                            </div>
                                        </div>



                                        <!-- 使用统计区域 -->
                                        <div class="recognition-type-config" id="usage-stats-section">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M3 3v18h18"/>
                                                        <path d="m19 9-5 5-4-4-3 3"/>
                                                    </svg>
                                                </div>
                                                <h4>使用统计</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div class="stats-grid">
                                                    <div class="stat-item">
                                                        <div class="stat-value" id="total-ocr-count">0</div>
                                                        <div class="stat-label">总识别次数</div>
                                                    </div>
                                                    <div class="stat-item">
                                                        <div class="stat-value" id="total-translate-count">0</div>
                                                        <div class="stat-label">总翻译次数</div>
                                                    </div>
                                                    <div class="stat-item">
                                                        <div class="stat-value" id="today-ocr-count">0</div>
                                                        <div class="stat-label">今日识别</div>
                                                    </div>
                                                    <div class="stat-item">
                                                        <div class="stat-value" id="today-translate-count">0</div>
                                                        <div class="stat-label">今日翻译</div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                    <!-- 默认模型配置 -->
                                    <div id="default-model-config" class="base-config-section" style="display: none;">
                                        <!-- 文字识别配置 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M15 12h6"/>
                                                        <path d="M15 6h6"/>
                                                        <path d="m3 13 3.553-7.724a.5.5 0 0 1 .894 0L11 13"/>
                                                        <path d="M3 18h18"/>
                                                        <path d="M3.92 11h6.16"/>
                                                    </svg>
                                                </div>
                                                <h4>文字识别</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div class="model-selection-row">
                                                    <label>默认模型:</label>
                                                    <div class="model-selection-controls">
                                                        <div class="model-select-container">
                                                            <button id="text-model-btn" class="model-select-btn" title="选择模型">
                                                                <span class="model-icon"></span>
                                                                <span class="model-text">请选择模型</span>
                                                                <span class="model-arrow">▼</span>
                                                            </button>
                                                            <div id="text-model-menu" class="model-select-menu">
                                                                <!-- 动态生成的模型列表 -->
                                                            </div>
                                                        </div>
                                                        <button type="button" class="config-btn" id="text-prompt-config" title="配置提示词">
                                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                                                                <path d="M14 17H5"/>
                                                                <path d="M19 7h-9"/>
                                                                <circle cx="17" cy="17" r="3"/>
                                                                <circle cx="7" cy="7" r="3"/>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 表格识别配置 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M15 3v18"/>
                                                        <rect width="18" height="18" x="3" y="3" rx="2"/>
                                                        <path d="M21 9H3"/>
                                                        <path d="M21 15H3"/>
                                                    </svg>
                                                </div>
                                                <h4>表格识别</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div class="model-selection-row">
                                                    <label>默认模型:</label>
                                                    <div class="model-selection-controls">
                                                        <div class="model-select-container">
                                                            <button id="table-model-btn" class="model-select-btn" title="选择模型">
                                                                <span class="model-icon"></span>
                                                                <span class="model-text">请选择模型</span>
                                                                <span class="model-arrow">▼</span>
                                                            </button>
                                                            <div id="table-model-menu" class="model-select-menu">
                                                                <!-- 动态生成的模型列表 -->
                                                            </div>
                                                        </div>
                                                        <button type="button" class="config-btn" id="table-prompt-config" title="配置提示词">
                                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                                                                <path d="M14 17H5"/>
                                                                <path d="M19 7h-9"/>
                                                                <circle cx="17" cy="17" r="3"/>
                                                                <circle cx="7" cy="7" r="3"/>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 公式识别配置 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M3 20h4.5a.5.5 0 0 0 .5-.5v-.282a.52.52 0 0 0-.247-.437 8 8 0 1 1 8.494-.001.52.52 0 0 0-.247.438v.282a.5.5 0 0 0 .5.5H21"/>
                                                    </svg>
                                                </div>
                                                <h4>公式识别</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div class="model-selection-row">
                                                    <label>默认模型:</label>
                                                    <div class="model-selection-controls">
                                                        <div class="model-select-container">
                                                            <button id="formula-model-btn" class="model-select-btn" title="选择模型">
                                                                <span class="model-icon"></span>
                                                                <span class="model-text">请选择模型</span>
                                                                <span class="model-arrow">▼</span>
                                                            </button>
                                                            <div id="formula-model-menu" class="model-select-menu">
                                                                <!-- 动态生成的模型列表 -->
                                                            </div>
                                                        </div>
                                                        <button type="button" class="config-btn" id="formula-prompt-config" title="配置提示词">
                                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                                                                <path d="M14 17H5"/>
                                                                <path d="M19 7h-9"/>
                                                                <circle cx="17" cy="17" r="3"/>
                                                                <circle cx="7" cy="7" r="3"/>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Markdown识别配置 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <rect width="18" height="18" x="3" y="3" rx="2"/>
                                                        <g transform="rotate(90 12 12)">
                                                            <path d="M16 8.9V7H8l4 5-4 5h8v-1.9"/>
                                                        </g>
                                                    </svg>
                                                </div>
                                                <h4>Markdown识别</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div class="model-selection-row">
                                                    <label>默认模型:</label>
                                                    <div class="model-selection-controls">
                                                        <div class="model-select-container">
                                                            <button id="markdown-model-btn" class="model-select-btn" title="选择模型">
                                                                <span class="model-icon"></span>
                                                                <span class="model-text">请选择模型</span>
                                                                <span class="model-arrow">▼</span>
                                                            </button>
                                                            <div id="markdown-model-menu" class="model-select-menu">
                                                                <!-- 动态生成的模型列表 -->
                                                            </div>
                                                        </div>
                                                        <button type="button" class="config-btn" id="markdown-prompt-config" title="配置提示词">
                                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                                                                <path d="M14 17H5"/>
                                                                <path d="M19 7h-9"/>
                                                                <circle cx="17" cy="17" r="3"/>
                                                                <circle cx="7" cy="7" r="3"/>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 翻译默认模型配置 -->
                                    <div id="translate-model-config" class="base-config-section" style="display: none;">
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20"/>
                                                        <path d="m8 13 4-7 4 7"/>
                                                        <path d="M9.1 11h5.7"/>
                                                    </svg>
                                                </div>
                                                <h4>翻译默认模型</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div class="form-group">
                                                    <div class="model-header">
                                                        <label>翻译模型:</label>
                                                        <div class="model-actions">
                                                            <button type="button" id="translate-add-model-btn" class="btn-small">添加</button>
                                                        </div>
                                                    </div>
                                                    <div class="model-list" id="translate-model-list">
                                                        <!-- 翻译模型列表将在这里动态生成 -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 可选配置 -->
                                    <div id="optional-settings-config" class="base-config-section" style="display: none;">
                                        <!-- OCR页面配置区域 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M3 7V5a2 2 0 0 1 2-2h2"/>
                                                        <path d="M17 3h2a2 2 0 0 1 2 2v2"/>
                                                        <path d="M21 17v2a2 2 0 0 1-2 2h-2"/>
                                                        <path d="M7 21H5a2 2 0 0 1-2-2v-2"/>
                                                        <path d="M7 8h8"/>
                                                        <path d="M7 12h10"/>
                                                        <path d="M7 16h6"/>
                                                    </svg>
                                                </div>
                                                <h4>OCR页面配置</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <!-- OCR页面自动清空 -->
                                                <div class="config-option-item">
                                                    <div class="config-option-info">
                                                        <div class="config-option-name" data-tooltip="页面切换、插件退出或窗口失去焦点时自动清空OCR识别结果">OCR页面自动清空</div>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="auto-clean-ocr-switch">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>

                                                <!-- OCR识别后自动复制 -->
                                                <div class="config-option-item">
                                                    <div class="config-option-info">
                                                        <div class="config-option-name" data-tooltip="OCR文字识别完成后自动将识别结果复制到系统剪贴板">OCR识别后自动复制</div>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="auto-copy-ocr-switch">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>

                                                <!-- OCR页面复制后自动关闭 -->
                                                <div class="config-option-item">
                                                    <div class="config-option-info">
                                                        <div class="config-option-name" data-tooltip="在OCR页面复制识别结果后自动隐藏插件窗口">OCR页面复制后自动关闭</div>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="auto-close-ocr-switch">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>

                                                <!-- 切换模型后自动重新识别 -->
                                                <div class="config-option-item">
                                                    <div class="config-option-info">
                                                        <div class="config-option-name" data-tooltip="在主界面切换模型或识别模式后自动重新识别当前图片">切换模型后自动重新识别</div>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="auto-reocr-switch">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 翻译页面配置区域 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="m5 8 6 6"/>
                                                        <path d="m4 14 6-6 2-3"/>
                                                        <path d="M2 5h12"/>
                                                        <path d="M7 2h1"/>
                                                        <path d="m22 22-5-10-5 10"/>
                                                        <path d="M14 18h6"/>
                                                    </svg>
                                                </div>
                                                <h4>翻译页面配置</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <!-- 自动翻译 -->
                                                <div class="config-option-item">
                                                    <div class="config-option-info">
                                                        <div class="config-option-name" data-tooltip="在翻译页面输入文本后，退出编辑状态后自动执行翻译（避免频发触发翻译浪费额度）">结束编辑后自动翻译</div>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="auto-translate-switch">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>

                                                <!-- 翻译页面自动清空 -->
                                                <div class="config-option-item">
                                                    <div class="config-option-info">
                                                        <div class="config-option-name" data-tooltip="页面切换、插件退出或窗口失去焦点时自动清空翻译内容">翻译页面自动清空</div>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="auto-clean-translate-switch">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>

                                                <!-- 翻译后自动复制 -->
                                                <div class="config-option-item">
                                                    <div class="config-option-info">
                                                        <div class="config-option-name" data-tooltip="翻译完成后自动将翻译结果复制到系统剪贴板">翻译后自动复制</div>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="auto-copy-translate-switch">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>

                                                <!-- 翻译页面复制后自动关闭 -->
                                                <div class="config-option-item">
                                                    <div class="config-option-info">
                                                        <div class="config-option-name" data-tooltip="在翻译页面复制翻译结果后自动隐藏插件窗口">翻译页面复制后自动关闭</div>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="auto-close-translate-switch">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 其他页面配置区域 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-grid2x2-plus-icon lucide-grid-2x2-plus">
                                                        <path d="M12 3v17a1 1 0 0 1-1 1H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v6a1 1 0 0 1-1 1H3"/>
                                                        <path d="M16 19h6"/>
                                                        <path d="M19 22v-6"/>
                                                    </svg>
                                                </div>
                                                <h4>其他页面配置</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <!-- 启用历史记录功能 -->
                                                <div class="config-option-item">
                                                    <div class="config-option-info">
                                                        <div class="config-option-name" data-tooltip="保存OCR识别和翻译历史记录，可在历史页面查看。可设置最大储存数量，超出后自动删除最旧记录。">启用历史记录功能</div>
                                                        <input type="number" id="history-max-count" min="10" max="1000" value="100" class="number-input history-count-input">
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="enable-history-switch">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>

                                                <!-- 自动故障恢复 -->
                                                <div class="config-option-item">
                                                    <div class="config-option-info">
                                                        <div class="config-option-name" data-tooltip="当前模型连接失败时自动切换到同平台的其他可用模型">自动故障恢复</div>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="auto-model-switch">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>

                                                <!-- 显示使用统计 -->
                                                <div class="config-option-item">
                                                    <div class="config-option-info">
                                                        <div class="config-option-name" data-tooltip="在个人中心显示OCR识别和翻译的使用次数统计信息">显示使用统计</div>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="show-usage-stats" checked>
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                    <!-- 快捷键配置 -->
                                    <div id="shortcuts-config" class="base-config-section" style="display: none;">
                                        <!-- 基础操作快捷键 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <rect x="3" y="3" width="7" height="7" rx="1"/>
                                                        <rect x="14" y="3" width="7" height="7" rx="1"/>
                                                        <rect x="3" y="14" width="7" height="7" rx="1"/>
                                                        <rect x="14" y="14" width="7" height="7" rx="1"/>
                                                    </svg>
                                                </div>
                                                <h4>基础操作</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="触发复制按钮（仅在OCR页面、翻译页面生效，其他页面保持系统默认行为）">复制识别/翻译结果</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="copyResult" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="启动截图功能进行OCR识别（在所有页面生效）">重新截图识别</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="takeScreenshot" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="重新识别当前图片（仅在OCR页面生效）">重新进行识别</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="reRecognize" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="清空当前显示的识别结果（仅在OCR页面、翻译页面生效）">清空识别/翻译结果</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="clearResult" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="根据当前页面智能触发对应的翻译按钮">触发翻译按钮功能</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="triggerTranslation" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 页面操作 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <rect width="18" height="18" x="3" y="3" rx="2"/>
                                                        <path d="M9 9h6v6H9z"/>
                                                    </svg>
                                                </div>
                                                <h4>页面操作</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="从任何页面直接跳转到基础配置页面">进入基础配置页面</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="openSettingsPage" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="从任何页面跳转到历史记录页面">进入历史记录页面</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="openHistoryPage" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="从任何页面跳转到OCR主页面">进入OCR页面</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="openOCRPage" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="从任何页面跳转到翻译页面">进入翻译页面</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="openTranslationPage" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="从任何页面跳转到模型服务配置页面">进入模型服务页面</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="openModelServicePage" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 功能切换 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
                                                        <circle cx="12" cy="12" r="3"/>
                                                    </svg>
                                                </div>
                                                <h4>功能切换</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="快速切换到文字识别模式（仅在OCR页面生效）">切换到文字识别模式</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="switchToText" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="快速切换到表格识别模式（仅在OCR页面生效）">切换到表格识别模式</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="switchToTable" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="快速切换到公式识别模式（仅在OCR页面生效）">切换到公式识别模式</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="switchToFormula" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="快速切换到Markdown识别模式（仅在OCR页面生效）">切换到Markdown识别模式</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="switchToMarkdown" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="在三种主题模式间循环切换（在所有页面生效）">插件主题颜色切换</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="toggleTheme" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="在开启和关闭换行符处理模式间切换（仅在OCR页面生效）">换行符模式切换</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="toggleLineBreakMode" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="在翻译页面前4个默认模型间循环切换（仅在翻译页面生效）">翻译模型切换</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="switchTranslationModel" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                                <div class="shortcut-item">
                                                    <div class="shortcut-info">
                                                        <div class="shortcut-name" data-tooltip="在历史记录页面的OCR和翻译分类间切换（仅在历史记录页面生效）">历史记录分类切换</div>
                                                    </div>
                                                    <div class="shortcut-input-container">
                                                        <input type="text" class="shortcut-input" data-key="switchHistoryCategory" readonly placeholder="点击录入快捷键">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                    </div>

                                    <!-- 数据备份与恢复配置 -->
                                    <div id="backup-restore-config" class="base-config-section" style="display: none;">
                                        <!-- 备份操作区域 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-archive-icon lucide-archive">
                                                        <rect width="20" height="5" x="2" y="3" rx="1"/>
                                                        <path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"/>
                                                        <path d="M10 12h4"/>
                                                    </svg>
                                                </div>
                                                <h4>创建备份</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div class="backup-options">
                                                    <div class="backup-option-item">
                                                        <label class="backup-checkbox-label">
                                                            <input type="checkbox" id="backup-include-config" checked>
                                                            <span class="backup-checkbox-text">配置数据</span>
                                                        </label>
                                                        <div class="backup-option-desc">备份核心配置和设置信息</div>
                                                    </div>
                                                    <div class="backup-option-item">
                                                        <label class="backup-checkbox-label">
                                                            <input type="checkbox" id="backup-include-personal" checked>
                                                            <span class="backup-checkbox-text">个人使用信息</span>
                                                        </label>
                                                        <div class="backup-option-desc">备份用户信息和使用统计</div>
                                                    </div>
                                                    <div class="backup-option-item">
                                                        <label class="backup-checkbox-label">
                                                            <input type="checkbox" id="backup-include-history" checked>
                                                            <span class="backup-checkbox-text">历史记录</span>
                                                        </label>
                                                        <div class="backup-option-desc">备份OCR识别和翻译的历史记录</div>
                                                    </div>
                                                    <div class="backup-option-item">
                                                        <label class="backup-checkbox-label">
                                                            <input type="checkbox" id="backup-include-cache" checked>
                                                            <span class="backup-checkbox-text">缓存数据</span>
                                                        </label>
                                                        <div class="backup-option-desc">备份模型列表和服务状态缓存</div>
                                                    </div>
                                                </div>
                                                <div class="backup-actions">
                                                    <button id="create-backup-btn" class="backup-btn primary">
                                                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                                            <polyline points="7,10 12,15 17,10"/>
                                                            <line x1="12" y1="15" x2="12" y2="3"/>
                                                        </svg>
                                                        创建备份
                                                    </button>
                                                    <div id="backup-progress" class="backup-progress" style="display: none;">
                                                        <div class="progress-bar">
                                                            <div class="progress-fill"></div>
                                                        </div>
                                                        <div class="progress-text">准备中...</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 恢复操作区域 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                                        <path d="M21 3v5h-5"/>
                                                        <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                                        <path d="M8 16H3v5"/>
                                                    </svg>
                                                </div>
                                                <h4>恢复备份</h4>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div class="restore-file-area">
                                                    <input type="file" id="restore-file-input" accept=".json" style="display: none;">
                                                    <div id="restore-drop-zone" class="restore-drop-zone">
                                                        <svg class="drop-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                                            <polyline points="17,8 12,3 7,8"/>
                                                            <line x1="12" y1="3" x2="12" y2="15"/>
                                                        </svg>
                                                        <div class="drop-text">
                                                            <div class="drop-main-text">点击上传备份文件</div>
                                                            <div class="drop-sub-text">或拖拽文件到此处</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="restore-file-info" class="restore-file-info" style="display: none;">
                                                    <div class="file-info-header">
                                                        <div class="file-name"></div>
                                                        <button class="file-remove-btn" title="移除文件">×</button>
                                                    </div>
                                                    <div class="file-details"></div>
                                                </div>
                                                <div class="restore-options" id="restore-options" style="display: none;">
                                                    <div class="restore-option-item">
                                                        <label class="backup-checkbox-label">
                                                            <input type="checkbox" id="restore-config" checked>
                                                            <span class="backup-checkbox-text">恢复配置数据</span>
                                                        </label>
                                                    </div>
                                                    <div class="restore-option-item">
                                                        <label class="backup-checkbox-label">
                                                            <input type="checkbox" id="restore-personal" checked>
                                                            <span class="backup-checkbox-text">恢复个人使用信息</span>
                                                        </label>
                                                    </div>
                                                    <div class="restore-option-item">
                                                        <label class="backup-checkbox-label">
                                                            <input type="checkbox" id="restore-history" checked>
                                                            <span class="backup-checkbox-text">恢复历史记录</span>
                                                        </label>
                                                    </div>
                                                    <div class="restore-option-item">
                                                        <label class="backup-checkbox-label">
                                                            <input type="checkbox" id="restore-cache" checked>
                                                            <span class="backup-checkbox-text">恢复缓存数据</span>
                                                        </label>
                                                    </div>
                                                    <div class="restore-option-item">
                                                        <label class="backup-checkbox-label">
                                                            <input type="checkbox" id="clear-existing">
                                                            <span class="backup-checkbox-text">清除现有数据</span>
                                                        </label>
                                                        <div class="backup-option-desc">恢复前清除所有现有数据（谨慎使用）</div>
                                                    </div>
                                                </div>
                                                <div class="restore-actions" id="restore-actions" style="display: none;">
                                                    <button id="restore-backup-btn" class="backup-btn primary">
                                                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                                            <path d="M21 3v5h-5"/>
                                                        </svg>
                                                        恢复备份
                                                    </button>
                                                    <div id="restore-progress" class="backup-progress" style="display: none;">
                                                        <div class="progress-bar">
                                                            <div class="progress-fill"></div>
                                                        </div>
                                                        <div class="progress-text">准备中...</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 当前数据状态 -->
                                        <div class="recognition-type-config">
                                            <div class="recognition-type-header">
                                                <div class="recognition-type-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database-icon lucide-database">
                                                        <ellipse cx="12" cy="5" rx="9" ry="3"/>
                                                        <path d="M3 5V19A9 3 0 0 0 21 19V5"/>
                                                        <path d="M3 12A9 3 0 0 0 21 12"/>
                                                    </svg>
                                                </div>
                                                <h4>当前数据状态</h4>
                                                <button id="refresh-data-status-btn" class="data-status-refresh-btn">
                                                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                                        <path d="M21 3v5h-5"/>
                                                        <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                                        <path d="M8 16H3v5"/>
                                                    </svg>
                                                </button>
                                            </div>
                                            <div class="recognition-type-content">
                                                <div id="current-data-summary" class="data-summary-categories">
                                                    <div class="summary-loading">正在加载数据状态...</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 底部导航栏 -->
                            <div class="config-bottom-nav">
                                <div class="left-controls">
                                    <button id="base-config-back-btn" class="control-btn" title="返回OCR页面">
                                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 7V5a2 2 0 0 1 2-2h2"/>
                                            <path d="M17 3h2a2 2 0 0 1 2 2v2"/>
                                            <path d="M21 17v2a2 2 0 0 1-2 2h-2"/>
                                            <path d="M7 21H5a2 2 0 0 1-2-2v-2"/>
                                            <path d="M7 8h8"/>
                                            <path d="M7 12h10"/>
                                            <path d="M7 16h6"/>
                                        </svg>
                                        <span class="btn-text">OCR</span>
                                    </button>
                                    <button id="base-config-translate-btn" class="control-btn" title="翻译">
                                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="m5 8 6 6"/>
                                            <path d="m4 14 6-6 2-3"/>
                                            <path d="M2 5h12"/>
                                            <path d="M7 2h1"/>
                                            <path d="m22 22-5-10-5 10"/>
                                            <path d="M14 18h6"/>
                                        </svg>
                                        <span class="btn-text">翻译</span>
                                    </button>
                                    <button id="base-config-history-btn" class="control-btn" title="查看识别历史">
                                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="8" height="4" x="8" y="2" rx="1" ry="1"/>
                                            <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                                            <path d="M12 11h4"/>
                                            <path d="M12 16h4"/>
                                            <path d="M8 11h.01"/>
                                            <path d="M8 16h.01"/>
                                        </svg>
                                        <span class="btn-text">历史记录</span>
                                    </button>
                                </div>
                                <div class="right-controls">
                                    <button id="base-config-base-config-btn" class="control-btn active" title="基础配置">
                                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <line x1="21" x2="14" y1="4" y2="4"/>
                                            <line x1="10" x2="3" y1="4" y2="4"/>
                                            <line x1="21" x2="12" y1="12" y2="12"/>
                                            <line x1="8" x2="3" y1="12" y2="12"/>
                                            <line x1="21" x2="16" y1="20" y2="20"/>
                                            <line x1="12" x2="3" y1="20" y2="20"/>
                                            <line x1="14" x2="14" y1="2" y2="6"/>
                                            <line x1="8" x2="8" y1="10" y2="14"/>
                                            <line x1="16" x2="16" y1="18" y2="22"/>
                                        </svg>
                                        <span class="btn-text">基础配置</span>
                                    </button>
                                    <button id="base-config-model-service-btn" class="control-btn" title="模型服务配置">
                                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z"/>
                                            <path d="M12 22V12"/>
                                            <polyline points="3.29 7 12 12 20.71 7"/>
                                            <path d="m7.5 4.27 9 5.15"/>
                                        </svg>
                                        <span class="btn-text">模型服务</span>
                                    </button>
                                    <button id="base-config-theme-toggle-btn" class="control-btn" title="切换主题">
                                        <svg class="btn-icon auto" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="12" cy="12" r="10"/>
                                            <path d="M12 18a6 6 0 0 0 0-12v12z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 模型服务配置页面 -->
                    <div id="model-service-page" class="config-page">
                    <!-- 左侧服务选择面板 -->
                    <div class="left-panel">
                        <!-- 服务列表区域 -->
                        <div class="service-list-area">
                            <div class="service-list">
                                <div class="service-item active" data-service="baidu">
                                    <div class="service-icon baidu-icon"></div>
                                    <span class="service-name">百度智能云</span>
                                    <div class="service-status-indicator" data-status="unknown"></div>
                                </div>
                                <div class="service-item" data-service="tencent">
                                    <div class="service-icon tencent-icon"></div>
                                    <span class="service-name">腾讯云</span>
                                    <div class="service-status-indicator" data-status="unknown"></div>
                                </div>
                                <div class="service-item" data-service="aliyun">
                                    <div class="service-icon aliyun-icon"></div>
                                    <span class="service-name">阿里云</span>
                                    <div class="service-status-indicator" data-status="unknown"></div>
                                </div>
                                <div class="service-item" data-service="openai">
                                    <div class="service-icon openai-icon"></div>
                                    <span class="service-name">OpenAI</span>
                                    <div class="service-status-indicator" data-status="unknown"></div>
                                </div>
                                <div class="service-item" data-service="anthropic">
                                    <div class="service-icon anthropic-icon"></div>
                                    <span class="service-name">Anthropic</span>
                                    <div class="service-status-indicator" data-status="unknown"></div>
                                </div>
                                <div class="service-item" data-service="google">
                                    <div class="service-icon google-icon"></div>
                                    <span class="service-name">Gemini</span>
                                    <div class="service-status-indicator" data-status="unknown"></div>
                                </div>
                                <div class="service-item" data-service="alibaba">
                                    <div class="service-icon alibaba-icon"></div>
                                    <span class="service-name">阿里云百炼</span>
                                    <div class="service-status-indicator" data-status="unknown"></div>
                                </div>
                                <div class="service-item" data-service="bytedance">
                                    <div class="service-icon bytedance-icon"></div>
                                    <span class="service-name">火山引擎</span>
                                    <div class="service-status-indicator" data-status="unknown"></div>
                                </div>
                                <div class="service-item" data-service="utools">
                                    <div class="service-icon utools-icon"></div>
                                    <span class="service-name">uTools AI</span>
                                    <div class="service-status-indicator" data-status="unknown"></div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <!-- 右侧配置面板 -->
                    <div class="right-panel">
                        <!-- 配置内容区域 -->
                        <div class="config-content-area">
                            <div class="config-content">
                                <!-- 隐藏原有的服务选择下拉框，保持兼容性 -->
                                <select id="ocr-service" class="select-input" style="display: none;">
                                    <option value="baidu" selected>百度智能云</option>
                                    <option value="tencent">腾讯云</option>
                                    <option value="aliyun">阿里云</option>
                                    <option value="openai">OpenAI</option>
                                    <option value="anthropic">Anthropic</option>
                                    <option value="google">Gemini</option>
                                    <option value="alibaba">阿里云百炼</option>
                                    <option value="bytedance">火山引擎</option>
                                    <option value="utools">uTools AI</option>

                                </select>
                
                <!-- 百度OCR配置 -->
                <div id="baidu-config" class="config-section">
                    <h4>百度智能云OCR配置</h4>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://console.bce.baidu.com/ai-engine/ocr/overview/index')" title="点击获取API Key">
                                <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                <path d="m21 2-9.6 9.6"/>
                                <circle cx="7.5" cy="15.5" r="5.5"/>
                            </svg>
                            API Key:
                        </label>
                        <div class="input-with-toggle">
                            <input type="password" id="baidu-api-key" placeholder="请输入百度智能云OCR API Key">
                            <button type="button" class="toggle-password" id="toggle-baidu-api-key" title="显示密码">
                                <span class="eye-icon"></span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://console.bce.baidu.com/ai-engine/ocr/overview/index')" title="点击获取API Key">
                                <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                <path d="m21 2-9.6 9.6"/>
                                <circle cx="7.5" cy="15.5" r="5.5"/>
                            </svg>
                            Secret Key:
                        </label>
                        <div class="input-with-toggle">
                            <input type="password" id="baidu-secret-key" placeholder="请输入百度智能云OCR Secret Key">
                            <button type="button" class="toggle-password" id="toggle-baidu-secret-key" title="显示密码">
                                <span class="eye-icon"></span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <button type="button" id="baidu-test-btn" class="btn btn-secondary ocr-test-btn">
                            <span class="test-btn-text">测试连接</span>
                        </button>
                    </div>

                    <!-- 百度翻译配置 -->
                    <div class="config-section">
                        <h4>百度智能云翻译配置</h4>
                        <div class="form-group">
                            <label>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://console.bce.baidu.com/ai/#/ai/machinetranslation/overview/index')" title="点击获取API Key">
                                    <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                    <path d="m21 2-9.6 9.6"/>
                                    <circle cx="7.5" cy="15.5" r="5.5"/>
                                </svg>
                                翻译API Key:
                            </label>
                            <div class="input-with-toggle">
                                <input type="password" id="baidu-translate-api-key" placeholder="请输入百度翻译API Key">
                                <button type="button" class="toggle-password" id="toggle-baidu-translate-api-key" title="显示密码">
                                    <span class="eye-icon"></span>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://console.bce.baidu.com/ai/#/ai/machinetranslation/overview/index')" title="点击获取API Key">
                                    <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                    <path d="m21 2-9.6 9.6"/>
                                    <circle cx="7.5" cy="15.5" r="5.5"/>
                                </svg>
                                翻译Secret Key:
                            </label>
                            <div class="input-with-toggle">
                                <input type="password" id="baidu-translate-secret-key" placeholder="请输入百度翻译Secret Key">
                                <button type="button" class="toggle-password" id="toggle-baidu-translate-secret-key" title="显示密码">
                                    <span class="eye-icon"></span>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="button" id="baidu-translate-test-btn" class="btn btn-secondary translate-test-btn">
                                <span class="test-btn-text">测试翻译连接</span>
                            </button>
                        </div>
                    </div>

                </div>
                
                <!-- 腾讯云OCR配置 -->
                <div id="tencent-config" class="config-section" style="display: none;">
                    <h4>腾讯云<span class="config-link" onclick="openApiKeyUrl('https://console.cloud.tencent.com/ocr/general')" title="开通OCR服务">OCR</span>配置</h4>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://console.cloud.tencent.com/cam/capi')" title="点击获取API Key">
                                <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                <path d="m21 2-9.6 9.6"/>
                                <circle cx="7.5" cy="15.5" r="5.5"/>
                            </svg>
                            Secret ID:
                        </label>
                        <div class="input-with-toggle">
                            <input type="password" id="tencent-secret-id" placeholder="请输入腾讯云Secret ID">
                            <button type="button" class="toggle-password" id="toggle-tencent-secret-id" title="显示密码">
                                <span class="eye-icon"></span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://console.cloud.tencent.com/cam/capi')" title="点击获取API Key">
                                <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                <path d="m21 2-9.6 9.6"/>
                                <circle cx="7.5" cy="15.5" r="5.5"/>
                            </svg>
                            Secret Key:
                        </label>
                        <div class="input-with-toggle">
                            <input type="password" id="tencent-secret-key" placeholder="请输入腾讯云Secret Key">
                            <button type="button" class="toggle-password" id="toggle-tencent-secret-key" title="显示密码">
                                <span class="eye-icon"></span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <button type="button" id="tencent-test-btn" class="btn btn-secondary ocr-test-btn">
                            <span class="test-btn-text">测试连接</span>
                        </button>
                    </div>

                    <!-- 腾讯云翻译配置 -->
                    <div class="config-section">
                        <h4>腾讯云<span class="config-link" onclick="openApiKeyUrl('https://console.cloud.tencent.com/tmt/settings')" title="开通翻译服务">翻译</span>配置</h4>
                        <div class="form-group">
                            <div class="config-item-row">
                                <div class="config-item-info">
                                    <div class="config-item-desc">翻译和OCR使用相同的Secret ID和Secret Key，确保已开启相关服务</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>地域:</label>
                            <div class="region-select-container">
                                <button id="tencent-translate-region" class="region-select-btn" title="选择地域">
                                    <span class="region-text">北京 (ap-beijing)</span>
                                    <span class="region-arrow">▼</span>
                                </button>
                                <div id="tencent-translate-region-menu" class="region-select-menu">
                                    <div class="region-option" data-value="ap-beijing">北京 (ap-beijing)</div>
                                    <div class="region-option" data-value="ap-shanghai">上海 (ap-shanghai)</div>
                                    <div class="region-option" data-value="ap-guangzhou">广州 (ap-guangzhou)</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="button" id="tencent-translate-test-btn" class="btn btn-secondary translate-test-btn">
                                <span class="test-btn-text">测试翻译连接</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 阿里云OCR配置 -->
                <div id="aliyun-config" class="config-section" style="display: none;">
                    <h4>阿里云<span class="config-link" onclick="openApiKeyUrl('https://ocr.console.aliyun.com/overview?spm=5176.12127803.J_5253785160.3.32f95542C8IZuA')" title="开通OCR服务">OCR</span>配置</h4>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://ram.console.aliyun.com/profile/access-keys?spm=5176.2020520153.console-base_top-nav.d_myaliyun_2_access_keys.7e46295c7s6Lxc')" title="点击获取API Key">
                                <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                <path d="m21 2-9.6 9.6"/>
                                <circle cx="7.5" cy="15.5" r="5.5"/>
                            </svg>
                            Access Key ID:
                        </label>
                        <div class="input-with-toggle">
                            <input type="password" id="aliyun-access-key" placeholder="请输入阿里云Access Key ID">
                            <button type="button" class="toggle-password" id="toggle-aliyun-access-key" title="显示密码">
                                <span class="eye-icon"></span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://ram.console.aliyun.com/profile/access-keys?spm=5176.2020520153.console-base_top-nav.d_myaliyun_2_access_keys.7e46295c7s6Lxc')" title="点击获取API Key">
                                <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                <path d="m21 2-9.6 9.6"/>
                                <circle cx="7.5" cy="15.5" r="5.5"/>
                            </svg>
                            Access Key Secret:
                        </label>
                        <div class="input-with-toggle">
                            <input type="password" id="aliyun-access-secret" placeholder="请输入阿里云Access Key Secret">
                            <button type="button" class="toggle-password" id="toggle-aliyun-access-secret" title="显示密码">
                                <span class="eye-icon"></span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <button type="button" id="aliyun-test-btn" class="btn btn-secondary ocr-test-btn">
                            <span class="test-btn-text">测试连接</span>
                        </button>
                    </div>

                    <!-- 阿里云翻译配置 -->
                    <div class="config-section">
                        <h4>阿里云<span class="config-link" onclick="openApiKeyUrl('https://www.aliyun.com/product/ai/alimt')" title="开通翻译服务">翻译</span>配置</h4>
                        <div class="form-group">
                            <div class="config-item-row">
                                <div class="config-item-info">
                                    <div class="config-item-desc">翻译和OCR使用相同的Access Key ID和Access Key Secret，确保已开启相关服务</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>地域:</label>
                            <div class="region-select-container">
                                <button id="aliyun-translate-region" class="region-select-btn" title="选择地域">
                                    <span class="region-text">上海 (cn-shanghai)</span>
                                    <span class="region-arrow">▼</span>
                                </button>
                                <div id="aliyun-translate-region-menu" class="region-select-menu">
                                    <div class="region-option" data-value="cn-shanghai">上海 (cn-shanghai)</div>
                                    <div class="region-option" data-value="cn-beijing">北京 (cn-beijing)</div>
                                    <div class="region-option" data-value="cn-hangzhou">杭州 (cn-hangzhou)</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="button" id="aliyun-translate-test-btn" class="btn btn-secondary translate-test-btn">
                                <span class="test-btn-text">测试翻译连接</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- OpenAI配置 -->
                <div id="openai-config" class="config-section" style="display: none;">
                    <h4>OpenAI配置</h4>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://platform.openai.com/api-keys')" title="点击获取API Key">
                                <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                <path d="m21 2-9.6 9.6"/>
                                <circle cx="7.5" cy="15.5" r="5.5"/>
                            </svg>
                            API Key:
                        </label>
                        <div class="input-with-toggle">
                            <input type="password" id="openai-api-key" placeholder="请输入OpenAI API Key">
                            <button type="button" class="toggle-password" id="toggle-openai-api-key" title="显示密码">
                                <span class="eye-icon"></span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon url-icon" onclick="openApiKeyUrl('https://openai.com')" title="访问OpenAI官网">
                                <path d="M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6"/>
                                <path d="m21 3-9 9"/>
                                <path d="M15 3h6v6"/>
                            </svg>
                            API Base URL:
                        </label>
                        <div class="input-with-reset">
                            <input type="text" id="openai-base-url" value="https://api.openai.com/v1" placeholder="API地址">
                            <button type="button" class="reset-url-btn" data-service="openai" data-default="https://api.openai.com/v1" title="重置为默认值">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                    <path d="M21 3v5h-5"/>
                                    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                    <path d="M3 21v-5h5"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="model-header">
                            <label>模型版本:</label>
                            <div class="model-actions">
                                <button type="button" id="openai-proxy-btn" class="btn-small">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                                        <path d="M2 17l10 5 10-5"/>
                                        <path d="M2 12l10 5 10-5"/>
                                    </svg>
                                    网络代理
                                </button>
                                <button type="button" id="openai-fetch-models-btn" class="btn-small">获取</button>
                                <button type="button" id="openai-add-model-btn" class="btn-small">添加</button>
                            </div>
                        </div>
                        <div class="model-list" id="openai-model-list">
                            <!-- 模型列表将在这里动态生成 -->
                        </div>
                    </div>

                </div>

                <!-- Anthropic配置 -->
                <div id="anthropic-config" class="config-section" style="display: none;">
                    <h4>Anthropic Claude配置</h4>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://console.anthropic.com/settings/keys')" title="点击获取API Key">
                                <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                <path d="m21 2-9.6 9.6"/>
                                <circle cx="7.5" cy="15.5" r="5.5"/>
                            </svg>
                            API Key:
                        </label>
                        <div class="input-with-toggle">
                            <input type="password" id="anthropic-api-key" placeholder="请输入Anthropic API Key">
                            <button type="button" class="toggle-password" id="toggle-anthropic-api-key" title="显示密码">
                                <span class="eye-icon"></span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon url-icon" onclick="openApiKeyUrl('https://anthropic.com/')" title="访问Anthropic官网">
                                <path d="M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6"/>
                                <path d="m21 3-9 9"/>
                                <path d="M15 3h6v6"/>
                            </svg>
                            API Base URL:
                        </label>
                        <div class="input-with-reset">
                            <input type="text" id="anthropic-base-url" value="https://api.anthropic.com/v1" placeholder="API地址">
                            <button type="button" class="reset-url-btn" data-service="anthropic" data-default="https://api.anthropic.com/v1" title="重置为默认值">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                    <path d="M21 3v5h-5"/>
                                    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                    <path d="M3 21v-5h5"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="model-header">
                            <label>模型版本:</label>
                            <div class="model-actions">
                                <button type="button" id="anthropic-proxy-btn" class="btn-small">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                                        <path d="M2 17l10 5 10-5"/>
                                        <path d="M2 12l10 5 10-5"/>
                                    </svg>
                                    网络代理
                                </button>
                                <button type="button" id="anthropic-fetch-models-btn" class="btn-small">获取</button>
                                <button type="button" id="anthropic-add-model-btn" class="btn-small">添加</button>
                            </div>
                        </div>
                        <div class="model-list" id="anthropic-model-list">
                            <!-- 模型列表将在这里动态生成 -->
                        </div>
                    </div>

                </div>

                <!-- Google Gemini配置 -->
                <div id="google-config" class="config-section" style="display: none;">
                    <h4>Google Gemini配置</h4>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://aistudio.google.com/app/apikey')" title="点击获取API Key">
                                <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                <path d="m21 2-9.6 9.6"/>
                                <circle cx="7.5" cy="15.5" r="5.5"/>
                            </svg>
                            API Key:
                        </label>
                        <div class="input-with-toggle">
                            <input type="password" id="google-api-key" placeholder="请输入Google API Key">
                            <button type="button" class="toggle-password" id="toggle-google-api-key" title="显示密码">
                                <span class="eye-icon"></span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon url-icon" onclick="openApiKeyUrl('https://gemini.google.com/')" title="访问Gemini官网">
                                <path d="M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6"/>
                                <path d="m21 3-9 9"/>
                                <path d="M15 3h6v6"/>
                            </svg>
                            API Base URL:
                        </label>
                        <div class="input-with-reset">
                            <input type="text" id="google-base-url" value="https://generativelanguage.googleapis.com" placeholder="API地址">
                            <button type="button" class="reset-url-btn" data-service="google" data-default="https://generativelanguage.googleapis.com" title="重置为默认值">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                    <path d="M21 3v5h-5"/>
                                    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                    <path d="M3 21v-5h5"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="model-header">
                            <label>模型版本:</label>
                            <div class="model-actions">
                                <button type="button" id="google-proxy-btn" class="btn-small">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                                        <path d="M2 17l10 5 10-5"/>
                                        <path d="M2 12l10 5 10-5"/>
                                    </svg>
                                    网络代理
                                </button>
                                <button type="button" id="google-fetch-models-btn" class="btn-small">获取</button>
                                <button type="button" id="google-add-model-btn" class="btn-small">添加</button>
                            </div>
                        </div>
                        <div class="model-list" id="google-model-list">
                            <!-- 模型列表将在这里动态生成 -->
                        </div>
                    </div>

                </div>

                <!-- 阿里云百炼配置 -->
                <div id="alibaba-config" class="config-section" style="display: none;">
                    <h4>阿里云百炼配置</h4>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://bailian.console.aliyun.com/?tab=model#/api-key')" title="点击获取API Key">
                                <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                <path d="m21 2-9.6 9.6"/>
                                <circle cx="7.5" cy="15.5" r="5.5"/>
                            </svg>
                            API Key:
                        </label>
                        <div class="input-with-toggle">
                            <input type="password" id="alibaba-api-key" placeholder="请输入阿里云百炼API Key">
                            <button type="button" class="toggle-password" id="toggle-alibaba-api-key" title="显示密码">
                                <span class="eye-icon"></span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon url-icon" onclick="openApiKeyUrl('https://www.aliyun.com/product/bailian')" title="访问阿里云百炼官网">
                                <path d="M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6"/>
                                <path d="m21 3-9 9"/>
                                <path d="M15 3h6v6"/>
                            </svg>
                            API Base URL:
                        </label>
                        <div class="input-with-reset">
                            <input type="text" id="alibaba-base-url" value="https://dashscope.aliyuncs.com/compatible-mode/v1" placeholder="API地址">
                            <button type="button" class="reset-url-btn" data-service="alibaba" data-default="https://dashscope.aliyuncs.com/compatible-mode/v1" title="重置为默认值">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                    <path d="M21 3v5h-5"/>
                                    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                    <path d="M3 21v-5h5"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="model-header">
                            <label>模型版本:</label>
                            <div class="model-actions">
                                <button type="button" id="alibaba-fetch-models-btn" class="btn-small">获取</button>
                                <button type="button" id="alibaba-add-model-btn" class="btn-small">添加</button>
                            </div>
                        </div>
                        <div class="model-list" id="alibaba-model-list">
                            <!-- 模型列表将在这里动态生成 -->
                        </div>
                    </div>

                </div>

                <!-- 火山引擎配置 -->
                <div id="bytedance-config" class="config-section" style="display: none;">
                    <h4>火山引擎配置</h4>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon api-key-icon" onclick="openApiKeyUrl('https://console.volcengine.com/ark/')" title="点击获取API Key">
                                <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"/>
                                <path d="m21 2-9.6 9.6"/>
                                <circle cx="7.5" cy="15.5" r="5.5"/>
                            </svg>
                            API Key:
                        </label>
                        <div class="input-with-toggle">
                            <input type="password" id="bytedance-api-key" placeholder="请输入火山引擎API Key">
                            <button type="button" class="toggle-password" id="toggle-bytedance-api-key" title="显示密码">
                                <span class="eye-icon"></span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="label-icon url-icon" onclick="openApiKeyUrl('https://www.volcengine.com/experience/ark?utm_term=202502dsinvite&ac=DSASUQY5&rc=DB4II4FC')" title="访问火山引擎官网">
                                <path d="M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6"/>
                                <path d="m21 3-9 9"/>
                                <path d="M15 3h6v6"/>
                            </svg>
                            API Base URL:
                        </label>
                        <div class="input-with-reset">
                            <input type="text" id="bytedance-base-url" value="https://ark.cn-beijing.volces.com/api/v3" placeholder="API地址">
                            <button type="button" class="reset-url-btn" data-service="bytedance" data-default="https://ark.cn-beijing.volces.com/api/v3" title="重置为默认值">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                    <path d="M21 3v5h-5"/>
                                    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                    <path d="M3 21v-5h5"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="model-header">
                            <label>模型版本:</label>
                            <div class="model-actions">
                                <button type="button" id="bytedance-fetch-models-btn" class="btn-small">获取</button>
                                <button type="button" id="bytedance-add-model-btn" class="btn-small">添加</button>
                            </div>
                        </div>
                        <div class="model-list" id="bytedance-model-list">
                            <!-- 模型列表将在这里动态生成 -->
                        </div>
                    </div>

                </div>

                <!-- OCR Pro配置 -->
                <div id="ocrpro-config" class="config-section" style="display: none;">
                    <h4>OCR Pro配置</h4>
                    <div class="form-group">
                        <div class="model-header">
                            <label>模型版本:</label>
                            <div class="model-actions">
                                <button type="button" id="ocrpro-fetch-models-btn" class="btn-small">获取</button>
                            </div>
                        </div>
                        <div class="model-list" id="ocrpro-model-list">
                            <!-- 模型列表将在这里动态生成 -->
                        </div>
                        <!-- OCR Pro服务说明 -->
                        <div class="ocrpro-service-notice">
                            <p>本模型为插件免费提供的服务，接入Gemini 2.5模型，可用作OCR识别和翻译服务，在个人中心可看到每天的使用额度。</p>
                            <p>由于是免费服务，不能保证服务的稳定性和质量，如报错可多次重试（如需稳定的服务，建议自行配置）。</p>
                            <p>合理使用，请勿浪费。</p>
                        </div>
                    </div>

                </div>

                <!-- uTools AI配置 -->
                <div id="utools-config" class="config-section" style="display: none;">
                    <h4>uTools AI配置</h4>
                    <div class="form-group">
                        <div class="model-header">
                            <label>模型版本:</label>
                            <div class="model-actions">
                                <button type="button" id="utools-fetch-models-btn" class="btn-small">获取</button>
                                <button type="button" id="utools-add-model-btn" class="btn-small">添加</button>
                            </div>
                        </div>
                        <div class="model-list" id="utools-model-list">
                            <!-- 模型列表将在这里动态生成 -->
                        </div>
                    </div>

                </div>




                            </div>
                        </div>

                        <!-- 底部导航栏 -->
                        <div class="config-bottom-nav">
                            <div class="left-controls">
                                <button id="back-btn" class="control-btn" title="返回OCR页面">
                                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M3 7V5a2 2 0 0 1 2-2h2"/>
                                        <path d="M17 3h2a2 2 0 0 1 2 2v2"/>
                                        <path d="M21 17v2a2 2 0 0 1-2 2h-2"/>
                                        <path d="M7 21H5a2 2 0 0 1-2-2v-2"/>
                                        <path d="M7 8h8"/>
                                        <path d="M7 12h10"/>
                                        <path d="M7 16h6"/>
                                    </svg>
                                    <span class="btn-text">OCR</span>
                                </button>
                                <button id="config-translate-btn" class="control-btn" title="翻译">
                                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m5 8 6 6"/>
                                        <path d="m4 14 6-6 2-3"/>
                                        <path d="M2 5h12"/>
                                        <path d="M7 2h1"/>
                                        <path d="m22 22-5-10-5 10"/>
                                        <path d="M14 18h6"/>
                                    </svg>
                                    <span class="btn-text">翻译</span>
                                </button>
                                <button id="config-history-btn" class="control-btn" title="查看识别历史">
                                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <rect width="8" height="4" x="8" y="2" rx="1" ry="1"/>
                                        <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                                        <path d="M12 11h4"/>
                                        <path d="M12 16h4"/>
                                        <path d="M8 11h.01"/>
                                        <path d="M8 16h.01"/>
                                    </svg>
                                    <span class="btn-text">历史记录</span>
                                </button>
                            </div>
                            <div class="right-controls">
                                <button id="base-config-btn" class="control-btn" title="基础配置">
                                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <line x1="21" x2="14" y1="4" y2="4"/>
                                        <line x1="10" x2="3" y1="4" y2="4"/>
                                        <line x1="21" x2="12" y1="12" y2="12"/>
                                        <line x1="8" x2="3" y1="12" y2="12"/>
                                        <line x1="21" x2="16" y1="20" y2="20"/>
                                        <line x1="12" x2="3" y1="20" y2="20"/>
                                        <line x1="14" x2="14" y1="2" y2="6"/>
                                        <line x1="8" x2="8" y1="10" y2="14"/>
                                        <line x1="16" x2="16" y1="18" y2="22"/>
                                    </svg>
                                    <span class="btn-text">基础配置</span>
                                </button>
                                <button id="model-service-btn" class="control-btn active" title="模型服务配置">
                                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z"/>
                                        <path d="M12 22V12"/>
                                        <polyline points="3.29 7 12 12 20.71 7"/>
                                        <path d="m7.5 4.27 9 5.15"/>
                                    </svg>
                                    <span class="btn-text">模型服务</span>
                                </button>
                                <button id="theme-toggle-btn" class="control-btn" title="切换主题">
                                    <svg class="btn-icon auto" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 18a6 6 0 0 0 0-12v12z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 历史记录界面 -->
        <div id="history-view" class="view" style="display: none;">
            <div class="main-container">
                <!-- 内容区域 -->
                <div class="content-container">
                    <!-- 左侧历史记录列表面板 -->
                    <div class="left-panel">
                        <!-- 历史记录类型切换按钮 -->
                        <div class="history-type-buttons">
                            <button id="history-ocr-btn" class="history-type-btn active" data-type="ocr">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 7V5a2 2 0 0 1 2-2h2"/>
                                    <path d="M17 3h2a2 2 0 0 1 2 2v2"/>
                                    <path d="M21 17v2a2 2 0 0 1-2 2h-2"/>
                                    <path d="M7 21H5a2 2 0 0 1-2-2v-2"/>
                                    <path d="M7 8h8"/>
                                    <path d="M7 12h10"/>
                                    <path d="M7 16h6"/>
                                </svg>
                                <span class="btn-text">OCR</span>
                            </button>
                            <button id="history-translate-type-btn" class="history-type-btn" data-type="translate">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m5 8 6 6"/>
                                    <path d="m4 14 6-6 2-3"/>
                                    <path d="M2 5h12"/>
                                    <path d="M7 2h1"/>
                                    <path d="m22 22-5-10-5 10"/>
                                    <path d="M14 18h6"/>
                                </svg>
                                <span class="btn-text">翻译</span>
                            </button>
                        </div>

                        <!-- 历史记录列表区域 -->
                        <div class="history-list-area">
                            <div class="history-list" id="history-list">
                                <!-- 历史记录项将通过JavaScript动态生成 -->
                                <div class="history-empty">
                                    <div class="empty-icon">📝</div>
                                    <div class="empty-text">暂无识别记录</div>
                                    <div class="empty-hint">完成OCR识别后记录将显示在这里</div>
                                </div>
                            </div>
                        </div>

                        <!-- 记录数量统计组件 -->
                        <div class="history-count-indicator">
                            <span id="history-count-text">共 0 条记录</span>
                        </div>
                    </div>

                    <!-- 右侧历史详情面板 -->
                    <div class="right-panel">
                        <div class="result-header" style="display: none;">
                        </div>

                        <div class="result-content">
                            <!-- 空状态显示 -->
                            <div class="history-detail-empty" id="history-detail-empty">
                                <div class="detail-empty-icon">👈</div>
                                <div class="detail-empty-text">选择左侧记录查看详情</div>
                                <div class="detail-empty-hint">记录详情将在此处显示</div>
                            </div>

                            <!-- OCR记录显示区域 -->
                            <div id="ocr-result-area" class="history-result-area" style="display: none;">
                                <textarea id="history-result-text" class="result-text" placeholder="识别结果将显示在这里..."></textarea>
                                <!-- 独立的复制按钮 -->
                                <button id="copy-btn-history" class="history-copy-btn" title="复制结果">
                                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                                        <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                                    </svg>
                                </button>
                            </div>

                            <!-- 翻译记录显示区域 -->
                            <div id="translate-result-area" class="history-result-area translate-dual-pane" style="display: none;">
                                <div class="translate-pane">
                                    <textarea id="history-translate-source-text" class="result-text" placeholder="原文内容..." readonly></textarea>
                                    <!-- 原文复制按钮 -->
                                    <button id="copy-btn-translate-source" class="history-copy-btn translate-source-copy-btn" title="复制原文">
                                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                                        </svg>
                                    </button>
                                </div>
                                <div class="translate-pane">
                                    <textarea id="history-translate-target-text" class="result-text" placeholder="译文内容..." readonly></textarea>
                                    <!-- 译文复制按钮 -->
                                    <button id="copy-btn-translate-target" class="history-copy-btn translate-target-copy-btn" title="复制译文">
                                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>



                        <!-- 底部导航栏 -->
                        <div class="config-bottom-nav">
                            <div class="left-controls">
                                <button id="history-back-btn" class="control-btn" title="返回OCR页面">
                                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M3 7V5a2 2 0 0 1 2-2h2"/>
                                        <path d="M17 3h2a2 2 0 0 1 2 2v2"/>
                                        <path d="M21 17v2a2 2 0 0 1-2 2h-2"/>
                                        <path d="M7 21H5a2 2 0 0 1-2-2v-2"/>
                                        <path d="M7 8h8"/>
                                        <path d="M7 12h10"/>
                                        <path d="M7 16h6"/>
                                    </svg>
                                    <span class="btn-text">OCR</span>
                                </button>
                                <button id="history-translate-btn" class="control-btn" title="翻译">
                                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m5 8 6 6"/>
                                        <path d="m4 14 6-6 2-3"/>
                                        <path d="M2 5h12"/>
                                        <path d="M7 2h1"/>
                                        <path d="m22 22-5-10-5 10"/>
                                        <path d="M14 18h6"/>
                                    </svg>
                                    <span class="btn-text">翻译</span>
                                </button>
                                <button id="history-history-btn" class="control-btn active" title="查看识别历史">
                                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <rect width="8" height="4" x="8" y="2" rx="1" ry="1"/>
                                        <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                                        <path d="M12 11h4"/>
                                        <path d="M12 16h4"/>
                                        <path d="M8 11h.01"/>
                                        <path d="M8 16h.01"/>
                                    </svg>
                                    <span class="btn-text">历史记录</span>
                                </button>
                            </div>
                            <div class="right-controls">
                                <button id="history-base-config-btn" class="control-btn" title="基础配置">
                                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <line x1="21" x2="14" y1="4" y2="4"/>
                                        <line x1="10" x2="3" y1="4" y2="4"/>
                                        <line x1="21" x2="12" y1="12" y2="12"/>
                                        <line x1="8" x2="3" y1="12" y2="12"/>
                                        <line x1="21" x2="16" y1="20" y2="20"/>
                                        <line x1="12" x2="3" y1="20" y2="20"/>
                                        <line x1="14" x2="14" y1="2" y2="6"/>
                                        <line x1="8" x2="8" y1="10" y2="14"/>
                                        <line x1="16" x2="16" y1="18" y2="22"/>
                                    </svg>
                                    <span class="btn-text">基础配置</span>
                                </button>
                                <button id="history-model-service-btn" class="control-btn" title="模型服务配置">
                                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z"/>
                                        <path d="M12 22V12"/>
                                        <polyline points="3.29 7 12 12 20.71 7"/>
                                        <path d="m7.5 4.27 9 5.15"/>
                                    </svg>
                                    <span class="btn-text">模型服务</span>
                                </button>
                                <button id="history-theme-toggle-btn" class="control-btn" title="切换主题">
                                    <svg class="btn-icon auto" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 18a6 6 0 0 0 0-12v12z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 翻译界面 -->
        <div id="translate-view" class="view" style="display: none;">
            <div class="main-container">
                <!-- 内容区域 -->
                <div class="content-container translate-container">
                    <!-- 源文本输入区域 -->
                    <div class="translate-input-section">
                        <div class="translate-input-area">
                            <textarea id="translate-source-text" class="translate-textarea" placeholder="请输入要翻译的文本..."></textarea>
                        </div>
                    </div>

                    <!-- 中间导航栏 -->
                    <div class="translate-controls">
                        <div class="left-controls">
                            <!-- 翻译模型快速选择 -->
                            <div id="translate-model-selector" class="translate-model-selector">
                                <!-- 翻译模型按钮将在这里动态生成 -->
                            </div>
                        </div>
                        <div class="center-controls">
                            <!-- 中间区域现在为空，可用于其他内容 -->
                        </div>
                        <div class="right-controls">
                            <!-- 语言选择器 -->
                            <div class="language-selector-container">
                                <!-- 源语言选择器 -->
                                <div class="language-selector">
                                    <button id="translate-source-language" class="language-select" title="源语言">
                                        <span class="language-text">自动检测</span>
                                    </button>
                                    <div id="translate-source-menu" class="language-select-menu">
                                        <!-- 语言选项将在这里动态生成 -->
                                    </div>
                                </div>

                                <!-- 语言交换按钮 -->
                                <button id="swap-languages-btn" class="language-swap-btn" title="交换语言">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M8 3L4 7l4 4"/>
                                        <path d="M4 7h16"/>
                                        <path d="M16 21l4-4-4-4"/>
                                        <path d="M20 17H4"/>
                                    </svg>
                                </button>

                                <!-- 目标语言选择器 -->
                                <div class="language-selector">
                                    <button id="translate-target-language" class="language-select" title="目标语言">
                                        <span class="language-text">英语</span>
                                    </button>
                                    <div id="translate-target-menu" class="language-select-menu">
                                        <!-- 语言选项将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 翻译结果显示区域 -->
                    <div class="translate-result-section">
                        <div class="translate-result-area">
                            <textarea id="translate-result-text" class="translate-result-content" placeholder="翻译结果将显示在这里，您可以直接编辑..."></textarea>
                            <!-- 底部检测区域 -->
                            <div class="translate-bottom-hover-area"></div>
                            <!-- 左下角导航按钮 -->
                            <button id="translate-settings-btn" class="translate-action-btn translate-settings-btn" title="设置">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                            <button id="translate-back-btn" class="translate-action-btn translate-back-btn" title="返回OCR页面">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 7V5a2 2 0 0 1 2-2h2"/>
                                    <path d="M17 3h2a2 2 0 0 1 2 2v2"/>
                                    <path d="M21 17v2a2 2 0 0 1-2 2h-2"/>
                                    <path d="M7 21H5a2 2 0 0 1-2-2v-2"/>
                                    <path d="M7 8h8"/>
                                    <path d="M7 12h10"/>
                                    <path d="M7 16h6"/>
                                </svg>
                            </button>
                            <button id="translate-history-btn" class="translate-action-btn translate-history-btn" title="查看历史记录">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect width="8" height="4" x="8" y="2" rx="1" ry="1"/>
                                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                                    <path d="M12 11h4"/>
                                    <path d="M12 16h4"/>
                                    <path d="M8 11h.01"/>
                                    <path d="M8 16h.01"/>
                                </svg>
                            </button>

                            <!-- 右下角操作按钮 -->
                            <!-- 清空按钮 -->
                            <button id="translate-clear-btn" class="translate-action-btn translate-clear-btn" title="清空内容">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m14.622 17.897-10.68-2.913"/>
                                    <path d="M18.376 2.622a1 1 0 1 1 3.002 3.002L17.36 9.643a.5.5 0 0 0 0 .707l.944.944a2.41 2.41 0 0 1 0 3.408l-.944.944a.5.5 0 0 1-.707 0L8.354 7.348a.5.5 0 0 1 0-.707l.944-.944a2.41 2.41 0 0 1 3.408 0l.944.944a.5.5 0 0 0 .707 0z"/>
                                    <path d="M9 8c-1.804 2.71-3.97 3.46-6.583 3.948a .507.507 0 0 0-.302.819l7.32 8.883a1 1 0 0 0 1.185.204C12.735 20.405 16 16.792 16 15"/>
                                </svg>
                            </button>
                            <!-- 翻译按钮 -->
                            <button id="translate-execute-btn" class="translate-action-btn translate-execute-btn" title="开始翻译">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m5 8 6 6"/>
                                    <path d="m4 14 6-6 2-3"/>
                                    <path d="M2 5h12"/>
                                    <path d="M7 2h1"/>
                                    <path d="m22 22-5-10-5 10"/>
                                    <path d="M14 18h6"/>
                                </svg>
                            </button>
                            <!-- 复制按钮 -->
                            <button id="translate-copy-btn" class="translate-action-btn translate-copy-btn" title="复制翻译结果">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                                    <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- 提示词配置弹窗 -->
    <div id="prompt-config-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="prompt-modal-title">配置提示词</h3>
                <div class="modal-header-actions">
                    <button type="button" class="modal-refresh-btn" id="prompt-save-header-btn" title="保存提示词配置">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-save-icon lucide-save">
                            <path d="M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z"/>
                            <path d="M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"/>
                            <path d="M7 3v4a1 1 0 0 0 1 1h7"/>
                        </svg>
                    </button>
                    <button type="button" class="modal-refresh-btn" id="prompt-reset-header-btn" title="重置为默认提示词">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-rotate-ccw-icon lucide-rotate-ccw">
                            <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
                            <path d="M3 3v5h5"/>
                        </svg>
                    </button>
                    <button type="button" class="modal-close" id="prompt-modal-close">&times;</button>
                </div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="prompt-textarea">提示词内容:</label>
                    <textarea id="prompt-textarea" class="prompt-textarea" rows="6" placeholder="请输入提示词内容..."></textarea>
                </div>
                <div class="prompt-tips">
                    <p><strong>提示:</strong> 提示词将指导AI模型如何处理图片识别任务。</p>
                    <p>例如：请识别图片中的所有文字内容，保持原有格式，直接输出文字，不要添加任何解释。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息弹窗 -->
    <div id="toast-container"></div>

    <!-- 引入所有模块 -->
    <!-- 模型获取弹窗 -->
    <div id="fetch-models-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>获取可用模型</h3>
                <div class="modal-header-actions">
                    <button type="button" class="modal-refresh-btn" id="fetch-models-refresh" title="刷新模型列表">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                            <path d="M21 3v5h-5"/>
                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                            <path d="M3 21v-5h5"/>
                        </svg>
                    </button>
                    <button type="button" class="modal-close" id="fetch-models-close">&times;</button>
                </div>
            </div>
            <div class="modal-body">
                <div id="fetch-models-loading" style="text-align: center; padding: 20px; display: none;">
                    <div style="color: var(--text-secondary);">正在获取模型列表...</div>
                </div>
                <div id="fetch-models-list" class="fetch-models-list">
                    <!-- 模型列表将在这里动态生成 -->
                </div>
                <div id="fetch-models-error" style="display: none; color: var(--error-color); text-align: center; padding: 20px;">
                    <!-- 错误信息将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 添加模型弹窗 -->
    <div id="add-model-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加自定义模型</h3>
                <button type="button" class="modal-close" id="add-model-close">&times;</button>
            </div>
            <div class="modal-body">
                <input type="text" id="add-model-input" class="add-model-input" placeholder="请输入模型ID，例如: gemini-2.0-flash-exp">
                <div class="add-model-hint">
                    请输入完整的模型名称。确保模型ID正确，否则可能无法正常使用。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="add-model-cancel" class="control-btn">
                    <span class="btn-text">取消</span>
                </button>
                <button type="button" id="add-model-confirm" class="control-btn">
                    <span class="btn-text">添加</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 翻译模型选择弹窗 -->
    <div id="translate-model-select-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>选择翻译模型</h3>
                <button type="button" class="modal-close" id="translate-model-select-close">&times;</button>
            </div>
            <div class="modal-body">
                <div id="translate-model-select-list" class="fetch-models-list">
                    <!-- 可用翻译模型列表将在这里动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 网络代理配置弹窗 -->
    <div id="proxy-config-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="proxy-modal-title">网络代理配置</h3>
                <div class="modal-header-actions">
                    <button type="button" class="modal-refresh-btn" id="proxy-test-header-btn" title="测试代理连接">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"/>
                        </svg>
                    </button>
                    <button type="button" class="modal-refresh-btn" id="proxy-save-header-btn" title="保存代理配置">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z"/>
                            <path d="M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"/>
                            <path d="M7 3v4a1 1 0 0 0 1 1h7"/>
                        </svg>
                    </button>
                    <button type="button" class="modal-close" id="proxy-modal-close">&times;</button>
                </div>
            </div>
            <div class="modal-body">
                <!-- 代理启用开关和状态显示 -->
                <div class="form-group">
                    <div class="proxy-enable-row">
                        <div class="proxy-enable-left">
                            <label for="proxy-enable-switch">启用网络代理</label>
                            <label class="switch">
                                <input type="checkbox" id="proxy-enable-switch">
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="proxy-status-container">
                            <div class="proxy-status-label">连接状态:</div>
                            <div id="proxy-status" class="proxy-status unknown">
                                <span class="status-indicator"></span>
                                <span class="status-text">未测试</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 代理配置表单 -->
                <div id="proxy-config-form" class="proxy-config-form">
                    <!-- 代理类型选择 -->
                    <div class="form-group">
                        <label for="proxy-type-select">代理类型:</label>
                        <div class="proxy-type-container">
                            <button id="proxy-type-select" class="proxy-type-btn" title="选择代理类型">
                                <span class="proxy-type-text">HTTP</span>
                                <span class="proxy-type-arrow">▼</span>
                            </button>
                            <div id="proxy-type-menu" class="proxy-type-menu">
                                <div class="proxy-type-option" data-value="http">HTTP</div>
                                <div class="proxy-type-option" data-value="https">HTTPS</div>
                                <div class="proxy-type-option" data-value="socks5">SOCKS5</div>
                            </div>
                        </div>
                    </div>

                    <!-- 代理服务器地址 -->
                    <div class="form-group">
                        <label for="proxy-host">代理服务器地址:</label>
                        <input type="text" id="proxy-host" class="proxy-input" placeholder="例如: 127.0.0.1 或 proxy.example.com">
                    </div>

                    <!-- 代理端口 -->
                    <div class="form-group">
                        <label for="proxy-port">代理端口:</label>
                        <input type="number" id="proxy-port" class="proxy-input" placeholder="例如: 1080" min="1" max="65535">
                    </div>

                    <!-- 认证信息 -->
                    <div class="form-group">
                        <div class="proxy-auth-header">
                            <label>认证信息 (可选)</label>
                            <label class="switch">
                                <input type="checkbox" id="proxy-auth-enable">
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div id="proxy-auth-fields" class="proxy-auth-fields" style="display: none;">
                            <div class="form-group">
                                <label for="proxy-username">用户名:</label>
                                <input type="text" id="proxy-username" class="proxy-input" placeholder="代理用户名">
                            </div>
                            <div class="form-group">
                                <label for="proxy-password">密码:</label>
                                <div class="input-with-toggle">
                                    <input type="password" id="proxy-password" class="proxy-input" placeholder="代理密码">
                                    <button type="button" class="toggle-password" id="toggle-proxy-password" title="显示密码">
                                        <span class="eye-icon"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 重置确认对话框 -->
    <div id="reset-confirm-modal" class="modal-overlay" style="display: none;">
        <div class="reset-modal-content">
            <div class="modal-header">
                <h3>确认重置</h3>
                <button type="button" class="modal-close" id="reset-modal-close">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <p>此操作会重置头像和用户名，是否确认？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="reset-cancel-btn">取消</button>
                <button type="button" class="btn btn-danger" id="reset-confirm-btn">确认</button>
            </div>
        </div>
    </div>

    <!-- 创建备份确认对话框 -->
    <div id="backup-create-confirm-modal" class="modal-overlay" style="display: none;">
        <div class="reset-modal-content">
            <div class="modal-header">
                <h3>确认创建备份</h3>
                <button type="button" class="modal-close" id="backup-create-modal-close">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <p>即将根据您选择的选项创建数据备份文件，备份完成后将自动下载到本地。是否确认创建备份？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="backup-create-cancel-btn">取消</button>
                <button type="button" class="btn btn-primary" id="backup-create-confirm-btn">确认创建</button>
            </div>
        </div>
    </div>

    <!-- 上传备份文件确认对话框 -->
    <div id="backup-upload-confirm-modal" class="modal-overlay" style="display: none;">
        <div class="reset-modal-content">
            <div class="modal-header">
                <h3>确认上传备份文件</h3>
                <button type="button" class="modal-close" id="backup-upload-modal-close">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <p>即将打开文件选择器，请选择要恢复的备份文件。请确保备份文件来源可信。是否确认继续？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="backup-upload-cancel-btn">取消</button>
                <button type="button" class="btn btn-primary" id="backup-upload-confirm-btn">确认上传</button>
            </div>
        </div>
    </div>

    <script src="src/katex-loader.js"></script>
    <script src="src/unified-storage-manager.js"></script>
    <script src="src/backup-restore-manager.js"></script>
    <script src="src/config.js"></script>
    <script src="src/ocrpro-quota-manager.js"></script>
    <script src="src/ocr-services.js"></script>
    <script src="src/image-zoom.js"></script>
    <script src="src/model-capability-detector.js"></script>
    <script src="src/ui.js"></script>
    <script src="src/model-manager.js"></script>
    <script src="src/model-status-manager.js"></script>
    <script src="src/history-manager.js"></script>
    <script src="src/drag-sort-manager.js"></script>
    <script src="src/capsule-window-manager.js"></script>
    <script src="src/main.js"></script>

    <!-- 数据详情弹窗 -->
    <div id="data-detail-modal" class="modal" style="display: none;">
        <div class="modal-content data-detail-modal-content">
            <div class="modal-header">
                <h3 id="data-detail-title">数据详情</h3>
                <button class="modal-close" onclick="closeDataDetailModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div id="data-detail-content" class="data-detail-content">
                    <!-- 数据详情内容将在这里动态生成 -->
                </div>
            </div>
        </div>
    </div>
</body>
</html>
